// HTMX
triggered = false;

document.body.addEventListener('htmx:configRequest', (event) => {
  event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
  triggered = true;
});

// Add active class to clicked nav link and remove for others
$('.sidebar-nav-link').on('click', function() {
  $('.sidebar-nav-link').removeClass('active');
  $(this).addClass('active');
});


// Close sidenav when a nav-link is clicked
document.body.querySelectorAll('.sidebar-navitem-close-on-click').forEach(element => {
  element.addEventListener('click', (e) =>{
    sidebar = document.querySelector('#sidebar')
     if(sidebar !== null && sidebar.classList.contains('in')) sidebar.classList.remove('in')
  })
});

function loadDatatable() {
  $.fn.dataTable.ext.errMode = 'none';
  $('#datatable').DataTable({
    error: function (xhr, error, thrown) {
      console.log('Datatables', error);
    },
    drawCallback: function() {
     $('.lazy:lt(4)').each(function() {
        $(this).attr('src', $(this).data('original')).removeClass('lazy');
     });
      $('img.lazy').lazyload({
        load: function() { $(this).removeClass("lazyload"); },
      });
      feather.replace();
      htmx.process(document.body);
    },
  });
}

(function () {
    htmx.on("htmx:afterSwap", (e) => {
      loadDatatable()
      
      // Response targeting #dialog => show the modal
      if (e.detail.target.id == "dialog") {
        $("#modal").modal("show")
      }
    })
  
    htmx.on("htmx:beforeSwap", (e) => {
      // Empty response targeting #dialog => hide the modal
      if (e.detail.target.id == "dialog" && !e.detail.xhr.response) {
        $("#modal").modal("hide")
        e.detail.shouldSwap = false
      }
    })
  
    // Remove dialog content after hiding
    $("#modal").on("hidden.bs.modal", () => {
      $("#dialog").empty()
    })
  })();

$(document).ready(function() {
    feather.replace();

    window.addEventListener('popstate', function(event) {
      location.replace(location.href);
    });

    $.extend( $.fn.dataTable.defaults, {
      language: {
          "sProcessing": "Traitement en cours...",
          "sSearch": "Rechercher&nbsp;:",
          "sLengthMenu": "Afficher _MENU_ &eacute;l&eacute;ments",
          "sInfo": "Affichage de l'&eacute;l&eacute;ment _START_ &agrave; _END_ sur _TOTAL_ &eacute;l&eacute;ments",
          "sInfoEmpty": "Affichage de l'&eacute;l&eacute;ment 0 &agrave; 0 sur 0 &eacute;l&eacute;ments",
          "sInfoFiltered": "(filtr&eacute; de _MAX_ &eacute;l&eacute;ments au total)",
          "sInfoPostFix": "",
          "sLoadingRecords": "Chargement en cours...",
          "sZeroRecords": "Aucun &eacute;l&eacute;ment &agrave; afficher",
          "sEmptyTable": "Aucune donn&eacute;e disponible dans le tableau",
          "oPaginate": {
              "sFirst": "Premier",
              "sPrevious": "Pr&eacute;c&eacute;dent",
              "sNext": "Suivant",
              "sLast": "Dernier"
          },
          "oAria": {
              "sSortAscending": ": activer pour trier la colonne par ordre croissant",
              "sSortDescending": ": activer pour trier la colonne par ordre d&eacute;croissant"
          }
      }
  });

  loadDatatable()
})

// Bottom Navigation
links = document.querySelectorAll('.bottom-nav-link')
links.forEach(link => {
  link.addEventListener('click', (ev) => {
    links.forEach(li => {
      li.classList.remove('active');
    })
    ev.target.closest('a').classList.add('active');
  })
}
)

function hideSubmitButtonIfFormValid(){
  $('#modal-content').on('submit', function() {
    if (this.checkValidity()) {
      $('#submit-btn').addClass('d-none');
      $('#spinner').removeClass('d-none'); 
      this.submit();
    }
    return false;
  });
}