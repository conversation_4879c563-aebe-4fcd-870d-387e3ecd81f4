{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">{{ form_title }}</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        {% if form_description %} <div class="alert alert-info">{{ form_description }}</div> {% endif %}
        <div class="form-row">
            {% for field in form %}
            <div class="form-group {% if field.name == 'job' or field.name == 'location' or field.name == 'school' or field.name == 'gender' or field.name == 'education' or field.name == 'date_hired' %} col-md-4 {% else %} {{ col_width|default:'col-12' }} {% endif %}" {% if field.name == 'school' %} id="school-select" {% endif %}>
                <label for="{{ field.id_for_label }}"><span class="text-muted">{{ field.label }}</span> {% if field.field.required %} <span class="text-danger">*</span> {% endif %}</label>
                {% if field.name == 'location' %}
                    {% render_field field class='form-control' hx-get='/ecoles-drena/' hx-target='#school-select' %}
                {% else %}
                    {% render_field field class='form-control' %}
                {% endif %}
                <span class="invalid-feedback">{{ field.errors|first }}</span>
        
                {% if field.help_text %}
                    <span class="form-text text-muted">{{ field.help_text }}</span>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    <div class="modal-footer">
        <div class="spinner-border d-none" role="status" id="spinner">
            <span class="sr-only">En cours...</span>
        </div>
        <button type="submit" id="submit-btn" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>
</form>

<script>
    hideSubmitButtonIfFormValid()


    if (typeof(flatpickr) !== "undefined") {
        // Select all input fields ending with 'date'
        document.querySelectorAll("input[name*='date']").forEach(function(element) {
            flatpickr(element, {
                dateFormat: "d/m/Y",
                enableTime: false,
                allowInput: true,
                timeZone: "Africa/Abidjan",
                disableMobile: true,
                locale: "fr",
            });
        });
    }

    $('.choices').addClass('d-flex align-items-center')
    $('.choices__inner').addClass('bg-white border-0 shadow-sm')

</script>