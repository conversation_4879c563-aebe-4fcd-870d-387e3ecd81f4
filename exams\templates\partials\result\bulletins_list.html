<nav aria-label="breadcrumb" role="navigation">
  <ol class="breadcrumb adminx-page-breadcrumb">
    <li class="breadcrumb-item">
      <a href="" hx-get="{% url 'home' %}"
         hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
    </li>
    <li class="breadcrumb-item active" aria-current="page">relevés</li>
  </ol>
</nav>

<div class="h5 my-3 font-weight-bold text-center">RELEVES ET ATTESTATIONS</div>

<div class="container mt-2 mb-2 mx-auto row d-flex justify-content-center">
    <div class="card col-md-4">
      <div class="card-header text-success">
        <strong>Relevés de notes {{ exam|upper }}</strong>
      </div>
      <div class="card-body container-fluid d-flex flex-column">
        {% for center in centers %}
        <div class="btn-group my-1">
          <a class="btn btn-sm btn-success btn-block text-white"
            hx-get="{% url 'bulletins_async' center.id exam %}"
            hx-target="#main-content">{{ center }}</a>
          <button type="button" class="btn btn-sm btn-success dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false" data-reference="parent">
            <span class="sr-only">Toggle Dropdown</span>
          </button>
          <div class="dropdown-menu">
            {% for room in center.room_set.all %}
              {% if room.enrollment_set.count > 15 %}
              <a class="dropdown-item"
                hx-get="{% url 'bulletins_async' center.id exam %}?salle={{room.number}}&part=1"
                hx-target="#main-content">{{ room}} - de 1 à 15</a>
              <a class="dropdown-item"
                hx-get="{% url 'bulletins_async' center.id exam %}?salle={{room.number}}&part=2"
                hx-target="#main-content">{{ room }} - de 16 à {{ room.enrollment_set.count }}</a>
              {% else %}
                <a class="dropdown-item"
                  hx-get="{% url 'bulletins_async' center.id exam %}?salle={{room.number}}"
                  hx-target="#main-content">{{ room }}</a>
              {% endif %}
            {% endfor %}
          </div>
        </div>
        {% endfor %}
        <div>
          <small class="text-muted">Les flèches permettent d'imprimer par salle.</small><br>
          <small class="text-info"><i data-feather="info" class="feather-12"></i> Traitement en arrière-plan.</small>
        </div>
      </div>
    </div>
    <div class="card col-md-4">
      <div class="card-header text-primary">
        <strong>Attestations de Bonne conduite {{ exam|upper }}</strong>
      </div>
      <div class="card-body container-fluid d-flex flex-column">
        {% for center in centers %}
        <div class="btn-group my-1">
          <a class="btn btn-sm btn-primary btn-block"
            hx-get="{% url 'attestations_async' center.id exam %}"
            hx-target="#main-content">{{ center }}</a>
          <button type="button" class="btn btn-sm btn-primary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false" data-reference="parent">
            <span class="sr-only">Toggle Dropdown</span>
          </button>
          <div class="dropdown-menu">
            {% for room in center.room_set.all %}
                <a class="dropdown-item"
                  hx-get="{% url 'attestations_async' center.id exam %}?salle={{room.number}}"
                  hx-target="#main-content">{{ room }}</a>
            {% endfor %}
          </div>
        </div>
        {% endfor %}
        <div>
          <small class="text-muted">Les flèches permettent d'imprimer par salle.</small><br>
          <small class="text-info"><i data-feather="info" class="feather-12"></i> Traitement en arrière-plan.</small>
        </div>
      </div>
    </div>
    <div class="card col-md-4">
      <div class="card-header text-danger">
        <strong>Diplomes {{ exam|upper }}</strong>
      </div>
      <div class="card-body container-fluid d-flex flex-column">
        {% for center in centers %}
        <div class="btn-group my-1">
          <a class="btn btn-sm btn-danger btn-block text-white"
            hx-get="{% url 'diplomas_async' center.id exam %}"
            hx-target="#main-content">{{ center }}</a>
          <button type="button" class="btn btn-sm btn-danger dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false" data-reference="parent">
            <span class="sr-only">Toggle Dropdown</span>
          </button>
          <div class="dropdown-menu">
            {% for room in center.room_set.all %}
                <a class="dropdown-item"
                  hx-get="{% url 'diplomas_async' center.id exam %}?salle={{room.number}}"
                  hx-target="#main-content">{{ room }}</a>
            {% endfor %}
          </div>
        </div>
        {% endfor %}
        <div>
          <small class="text-muted">Les flèches permettent d'imprimer par salle.</small><br>
          <small class="text-info"><i data-feather="info" class="feather-12"></i> Traitement en arrière-plan.</small>
        </div>
      </div>
    </div>
  </div>
<div class="col-md-6 mx-auto mb-5">
  <div>Centre:</div>
  <select name="center" id="center"
          class="form-control" required="required"
          hx-get="{% url 'bulletins_list' %}"
          hx-target="#main-content">
    <option value="0">Sélectionner</option>
    {% for center in centers %}
    <option value="{{ center.id }}"
    {% if center_id == center.id %} selected {% endif %}>{{ center }}</option>
    {% empty %}
    {% endfor %}
  </select>
</div>

  {% if display_results %}
  <h5 class="font-weight-bold ml-3">LISTE DES ELEVES</h5>

  <div class="row px-2" id="table-wrapper">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-bordered table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white text-center">PHOTO</th>
                <th scope="col" class="text-white">N° TABLE</th>
                <th scope="col" class="text-white" style="min-width: 120px">NOM ET PRENOMS</th>
                <th scope="col" class="text-white">POINTS</th>
                <th scope="col" class="text-white">MOYENNE</th>
                <th scope="col" class="text-white text-left" style="max-width: 15px;">BULL.</th>
                <th scope="col" class="text-white text-left" style="max-width: 15px;">ATT.</th>
                <th scope="col" class="text-white text-left" style="max-width: 15px;">DIPL.</th>
              </tr>
            </thead>
            <tbody>
              {% for candidate in candidates %}
              <tr>
                <td class="align-middle text-center" style="min-width: 60px;">
                  {% if candidate.student.photo %}
                    <img data-original="{{ candidate.student.photo.url }}"
                        alt="1"
                        class="lazy border img-thumbnail rounded-circle">
                  {% endif %}
                </td>
                <td class="align-middle">{{ candidate.table_num }}</td>
                <td class="align-middle">{{ candidate }}</td>
                <td class="align-middle">{{ candidate.total|floatformat:0 }}</td>
                <td class="align-middle">{{ candidate.average|floatformat:2 }}</td>
                <td><a class="btn btn-success btn-sm"
                  hx-get="{% url 'bulletins_async' candidate.center.id exam %}?table_num={{ candidate.table_num }}"
                  hx-target="#main-content"><i data-feather="file-text"></i></a></td>
                <td><a class="btn btn-primary btn-sm"
                  hx-get="{% url 'attestations_async' candidate.center.id exam %}?table_num={{ candidate.table_num }}"
                  hx-target="#main-content"><i data-feather="file-text"></i></a></td>
                <td><a class="btn btn-danger btn-sm"
                  hx-get="{% url 'diplomas_async' candidate.center.id exam %}?table_num={{ candidate.table_num }}"
                  hx-target="#main-content"><i data-feather="file-text"></i></a></td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %}
<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace();
  }
</script>