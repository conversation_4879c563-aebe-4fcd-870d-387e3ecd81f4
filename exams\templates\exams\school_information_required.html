{% extends 'base_minimal.html' %}
{% load static %}

{% block title %}Mise à jour des informations - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .btn-outline-danger {
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .min-vh-100 {
        min-height: 100vh;
    }

    .logo-img {
        height: 50px;
        width: auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 768px) {
        .logo-img {
            height: 40px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center min-vh-75 align-items-center">
        <div class="col-md-8 col-lg-6">
            <!-- Header with Logo and Logout -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex align-items-center">
                    <img src="{% static 'images/logo.jpg' %}" alt="Logo" class="logo-img me-3">
                </div>
                <div>
                    <a href="{% url 'logout' %}" class="btn btn-outline-danger btn-sm">
                        <i data-feather="log-out" class="me-1"></i>
                        Déconnexion
                    </a>
                </div>
            </div>

            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-primary text-white text-center py-3">
                    <h4 class="mb-0">
                        <i data-feather="info" class="me-2"></i>
                        {% if step == 'payment' %}
                            Paiement Requis
                        {% else %}
                            Informations Requises
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <div class="alert alert-info border-0 rounded-lg">
                            <h6 class="alert-heading">
                                <i data-feather="alert-circle" class="me-2"></i>
                                {% if step == 'payment' %}
                                    Félicitations!
                                {% else %}
                                    Information:
                                {% endif %}
                            </h6>
                            <p class="mb-0 small">
                                {% if step == 'payment' %}
                                    Les informations et statistiques de votre établissement <span class="font-weight-bold">{{ user.school }}</span> ont été mises à jour avec succès.
                                {% else %}
                                    Afin d'améliorer la gestion des examens et des établissements CHERIFLA et affiliés, la DEC CHERIFLA a besoin de
                                    faire une vérification et une mise à jour des informations de votre établissement: <span class="font-weight-bold">{{ user.school }}</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card border bg-light shadow-sm">
                                <div class="card-body text-center">
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <i data-feather="{% if information_updated %}check-circle{% else %}alert-circle{% endif %}"
                                               class="{% if information_updated %}text-success{% else %}text-warning{% endif %} mb-2"
                                               style="width: 24px; height: 24px;"></i>
                                            <h6 class="{% if information_updated %}text-success{% else %}text-warning{% endif %} mb-1">1. Informations générales</h6>
                                            <small class="text-muted">{% if information_updated %}Complétées{% else %}À compléter{% endif %}</small>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <i data-feather="{% if stats_updated %}check-circle{% else %}alert-circle{% endif %}"
                                               class="{% if stats_updated %}text-success{% else %}text-warning{% endif %} mb-2"
                                               style="width: 24px; height: 24px;"></i>
                                            <h6 class="{% if stats_updated %}text-success{% else %}text-warning{% endif %} mb-1">2. Statistiques - {% if stats_updated %}Complétées{% else %}À compléter{% endif %}</h6>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <i data-feather="{% if has_fees_record %}check-circle{% else %}alert-circle{% endif %}"
                                               class="{% if has_fees_record %}text-success{% else %}text-warning{% endif %} mb-2"
                                               style="width: 24px; height: 24px;"></i>
                                            <h6 class="{% if has_fees_record %}text-success{% else %}text-warning{% endif %} mb-1">3. Paiement - {% if has_fees_record %}Confirmé{% else %}En attente{% endif %}</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        {% if step == 'payment' %}
                            <div class="alert alert-warning border-0 rounded-lg mb-3">
                                <h6 class="alert-heading">
                                    <i data-feather="phone" class="me-2"></i>
                                    Contact DEC CHERIFLA
                                </h6>
                                <p class="mb-0 small">
                                    Veuillez contacter la DEC CHERIFLA pour finaliser le paiement des droits annuels et confirmer votre inscription.
                                </p>
                            </div>
                        {% else %}
                            <a href="{% url 'school_information_wizard' %}"
                               class="btn btn-primary px-4 py-2 rounded-pill shadow-sm wobble-animation">
                                <i data-feather="play" class="me-2"></i>
                                Commencer la mise à jour
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes wobble {
    0% { transform: translateX(0%); }
    15% { transform: translateX(-25px) rotate(-5deg); }
    30% { transform: translateX(20px) rotate(3deg); }
    45% { transform: translateX(-15px) rotate(-3deg); }
    60% { transform: translateX(10px) rotate(2deg); }
    75% { transform: translateX(-5px) rotate(-1deg); }
    100% { transform: translateX(0%); }
}

.wobble-animation {
    animation: wobble 1s ease-in-out infinite;
    animation-delay: 1s;
}

.wobble-animation:hover {
    animation-play-state: paused;
}
</style>

{% endblock %}
