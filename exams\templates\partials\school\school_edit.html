{% load widget_tweaks %}

{% with WIDGET_ERROR_CLASS='is-invalid' %}
<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">ECOLE {{ school|upper }}</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Informations sur l'école</div>
        </div>

        <!-- New fields section -->
        <div class="form-row">
            <div class="form-group mb-2 col-6">
                <label for="{{ form.school_type.id_for_label }}">Type d'école (ECC/ESIA) <span class="text-danger">*</span></label>
                {% render_field form.school_type class='form-control' %}
                <div class="invalid-feedback">{{ form.school_type.errors|first }}</div>
            </div>
            <div class="form-group mb-2 col-6">
                <label for="{{ form.school_cycle.id_for_label }}">Cycle de l'école <span class="text-danger">*</span></label>
                {% render_field form.school_cycle class='form-control' %}
                <div class="invalid-feedback">{{ form.school_cycle.errors|first }}</div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-6">
                <label for="{{ form.school_status.id_for_label }}">Statut de l'école (Reconnu/Non rec.) <span class="text-danger">*</span></label>
                {% render_field form.school_status class='form-control' %}
                <div class="invalid-feedback">{{ form.school_status.errors|first }}</div>
            </div>
            <div class="form-group mb-2 col-6">
                <label for="{{ form.teachers_count.id_for_label }}">Nombre d'enseignants <span class="text-danger">*</span></label>
                {% render_field form.teachers_count class='form-control' %}
                <div class="invalid-feedback">{{ form.teachers_count.errors|first }}</div>
            </div>
        </div>

        <!-- Existing fields section -->
        <div class="form-row">
            <div class="form-group mb-2 col-6">
                <label for="{{ form.drena_obj.id_for_label }}">* DRENA</label>
                {% render_field form.drena_obj class='form-control'%}
                <div class="invalid-feedback">{{ form.drena_obj.errors|first }}</div>
            </div>
            <div class="form-group mb-2 col-6">
                <label for="{{ form.iepp.id_for_label }}">* IEPP</label>
                 {% render_field form.iepp class='form-control text-uppercase' %}
                 <div class="invalid-feedback">{{ form.iepp.errors|first }}</div>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.local_commission.id_for_label }}">Commission Locale *</label>
                {% render_field form.local_commission class='form-control'%}
                <div class="invalid-feedback">{{ form.local_commission.errors|first }}</div>
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.name.id_for_label }}">Nom de l'école *</label>
                 {% render_field form.name class='form-control text-uppercase' %}
                 <div class="invalid-feedback">{{ form.name.errors|first }}</div>
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.name_ar.id_for_label }}">Traduction en arabe (optionel)</label>
                {% render_field form.name_ar class='form-control' %}
                <div class="invalid-feedback">{{ form.name_ar.errors|first }}</div>
            </div>
        </div>

        <div class="form-row mt-3">
            <p class="lead fw-bold text-muted">Localité et Directeur</p>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.director.id_for_label }}">Ville/Village/Campement de l'école</label>
                {% render_field form.location class='form-control text-uppercase' required='required' %}
                <div class="invalid-feedback">{{ form.location.errors|first }}</div>
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.director.id_for_label }}">Nom et prénoms du Directeur</label>
                {% render_field form.director class='form-control text-uppercase' %}
                <div class="invalid-feedback">{{ form.director.errors|first }}</div>
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.phone.id_for_label }}">Contact du directeur</label>
                {% render_field form.phone class='form-control' %}
                <p class="form-text text-muted small">
                    Ce numéro servira comme mot de passe de l'espace école concerné
                </p>
                <div class="invalid-feedback">{{ form.phone.errors|first }}</div>

            </div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="spinner-border d-none" role="status" id="spinner">
            <span class="sr-only">En cours...</span>
        </div>
        <button type="submit" class="btn btn-success" id="submit-btn"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>

<script>
    hideSubmitButtonIfFormValid()
</script>
{% endwith %}