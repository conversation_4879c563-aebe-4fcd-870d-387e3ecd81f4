{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Candidates admis par centre</li>
    </ol>
  </nav>

  <h4 class="pb-3 my-2 text-center">
    Candidats admis: <strong class="text-success">{{ all_admitted }} / {{ all_candidates }}</strong>
  </h4>

  <div class="flex justify-content-between my-2 text-center">
    <a href="{% url 'results_pdf' %}?exam={{selected_exam}}" class="btn btn-sm btn-success"><span data-feather="file-text"></span> Liste détaillé</a>
    <a href="{% url 'results_pdf' %}?summarized=1&exam={{selected_exam}}" class="btn btn-sm btn-warning"><span data-feather="file-text"></span>Liste résumé</a>
  </div>

  <div class="row" id="table-wrapper">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" style="min-width: 120px;" class="text-white">CENTRE</th>
                <th scope="col" class="text-white">CANDIDATS</th>
                <th scope="col" class="text-white">ADMIS</th>
                <th scope="col" class="text-white">REPÊCHES</th>
                <th scope="col" class="text-white">TOTAL</th>
              </tr>
            </thead>
            <tbody>
              {% for center in centers %}
              <tr>
                <td>{{ center }}</td>
                <td>{{ center.candidates }}</td>
                <td>{{ center.admitted }}</td>
                <td>{{ center.admissible }}</td>
                <td>{{ center.all_admitted }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>