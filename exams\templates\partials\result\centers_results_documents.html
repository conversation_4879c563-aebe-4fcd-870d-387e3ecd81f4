<nav aria-label="breadcrumb" role="navigation">
  <ol class="breadcrumb adminx-page-breadcrumb">
    <li class="breadcrumb-item">
      <a href="" hx-get="{% url 'home' %}" 
         hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
    </li>
    <li class="breadcrumb-item active" aria-current="page">Résultats</li>
  </ol>
</nav>

<div class="h5 my-3 font-weight-bold text-center">RESULTATS PAR CENTRE MODELE {% if lang == 'fr' %} (FRANCAIS) {% else %} (ARABE) {% endif %}</div>

<div class="container mt-2 mb-5 mx-auto row">
    <!-- <div class="card col-md-4">
      <div class="card-header text-success">
        <strong>Résultats avec matières (par mérite)</strong>
      </div>
      <div class="card-body container-fluid d-flex flex-column">
        {% for center in centers %}
          <a href="{% url 'center_results_pdf' center.id %}?ordre=merite&lang={{lang}}" 
             class="btn btn-success btn-sm my-1" style="min-width: auto">{{ center }}</a>
        {% endfor %}
        
        {% if user.role == ROLE_COMMISSION_LOCALE %}
          <a href="{% url 'center_results_pdf' centers.first.id %}?ordre=merite&lang={{lang}}&tous=1" 
            class="btn btn-outline-success btn-sm my-1" style="min-width: auto">POUR TOUTE LA LOCALITE</a>
        {% endif %}
       </div>
    </div> -->
    <div class="card col-md-4">
      <div class="card-header text-success">
        <strong>Résultats (par ordre de mérite)</strong>
      </div>
      <div class="card-body container-fluid d-flex flex-column">
        {% for center in centers %}
          <a href="{% url 'center_results_pdf' center.id %}?ordre=merite&resume=1&lang={{lang}}" class="btn btn-success btn-sm my-1" style="min-width: auto">{{ center }}</a>
        {% endfor %}

        {% if user.role == ROLE_COMMISSION_LOCALE %}
          <a href="{% url 'center_results_pdf' centers.first.id %}?ordre=merite&resume=1&lang={{lang}}&tous=1" 
            class="btn btn-outline-success btn-sm my-1" style="min-width: auto">POUR TOUS LES CENTRES</a>
        {% endif %}
      </div>
    </div>
    <!-- <div class="card col-md-4">
      <div class="card-header">
        <strong>Résultats avec matières (alphabétique)</strong>
      </div>
      <div class="card-body container-fluid d-flex flex-column">
        {% for center in centers %}
          <a href="{% url 'center_results_pdf' center.id %}?ordre=alpha&lang={{lang}}" 
             class="btn btn-warning btn-sm my-1" style="min-width: auto">{{ center }}</a>
        {% endfor %}

        {% if user.role == ROLE_COMMISSION_LOCALE %}
        <a href="{% url 'center_results_pdf' centers.first.id %}?ordre=alpha&lang={{lang}}&tous=1&exam={{exam}}" 
          class="btn btn-outline-warning btn-sm my-1" style="min-width: auto">POUR TOUS LES CENTRES</a>
        {% endif %}
      </div>
    </div> -->
    <div class="card col-md-4">
      <div class="card-header text-info">
        <strong>Résultats (par ordre alphabétique)</strong>
      </div>
      <div class="card-body container-fluid d-flex flex-column">
        {% for center in centers %}
          <a href="{% url 'center_results_pdf' center.id %}?ordre=alpha&resume=1&lang={{lang}}" 
             class="btn btn-info btn-sm my-1" style="min-width: auto">{{ center }}</a>
        {% endfor %}

        {% if user.role == ROLE_COMMISSION_LOCALE %}
        <a href="{% url 'center_results_pdf' centers.first.id %}?ordre=alpha&resume=1&lang={{lang}}&tous=1&exam={{exam}}" 
          class="btn btn-outline-info btn-sm my-1" style="min-width: auto">POUR TOUTE LA LOCALITE</a>
        {% endif %}
      </div>
    </div>
  </div>