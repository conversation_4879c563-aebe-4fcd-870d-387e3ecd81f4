{% load static %}
{% load humanize %}
{% load widget_tweaks %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Examen Blanc</li>
      <li class="breadcrumb-item active" aria-current="page">Exportation</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">EXPORTER LES MOYENNES D'EXAMEN BLANC DANS UN FICHIER EXCEL</h5>
  </div>

  <div class="card mb-grid">
    <div class="card-header d-flex justify-content-between align-items-center">
      <div class="card-header-title">Sélectionner une école</div>
    </div>
    <div class="card-body">
      <form action="{{ action }}" method="post">
        {% csrf_token %}
        
        <div class="form-row mb-3">
          <div class="col-md-6">
            <label for="school">École</label>
            <select name="school" id="school" class="form-control" required>
              <option value="">Sélectionner une école</option>
              {% for school in schools %}
                <option value="{{ school.id }}">{{ school.name }}</option>
              {% endfor %}
            </select>
          </div>
        </div>
        
        <div class="col-md-12">
          <button type="submit" class="btn btn-success">
            <span data-feather="arrow-down"></span>
            Exporter vers Excel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>
