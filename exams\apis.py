from datetime import date
from .models import Student, Enrollment, School
from project_utils.constants import ROLE_ECOLE
from django.contrib.auth import get_user_model
from project_utils.custom_utils import get_current_year
from django.views.decorators.csrf import csrf_exempt
from django.http import HttpResponse

@csrf_exempt
def students(request):
    print('Request received', request.method)
    data = dict(request.POST)

    if request.method == 'POST':
        data = dict(request.POST)
        school_id = data.pop('school_id')[0]
        school = School.objects.get(id=school_id)
        user = get_user_model().objects.filter(
            school__id=school_id, role=ROLE_ECOLE).first()
        students_to_create = []
        enrollments_to_create = []
        print('A post request was made', request.POST, request.POST.items())
        for key, value in data.items():
            obj = Student(
                last_name=value[0],
                first_name=value[1],
                full_name_ar=value[2],
                gender=value[3],
                birth_date=date(int(value[6]), int(value[5]), int(value[4])),
                birth_place=value[7],
                birth_place_ar=value[8],
                photo=value[9],
                father=value[10],
                mother=value[11],
                school=school,
            )
            obj.save(value[12])
        
            Enrollment.objects.create(
                year=get_current_year(),
                student=obj,
                exam=value[12],
                agent=user,
                school=school,
                ecolepro_id=int(key)
            )
            print('Objects saved')
    return HttpResponse(status=200)