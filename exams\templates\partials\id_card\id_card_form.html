{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">DEMANDE DE CARTE SCOLAIRE</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold">Infos Elève</div>
        </div>
        <section class="form-row">
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.matricule_dsps.id_for_label }}">Matricule DSPS</label>
                 {% render_field form.matricule_dsps class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.last_name.id_for_label }}">* Nom</label>
                 {% render_field form.last_name class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6" id="location_centers">
                <label for="{{ form.first_name.id_for_label }}">* Prénoms</label>
                 {% render_field form.first_name class='form-control text-uppercase' %}
            </div>
        </section>
        <section class="form-row">
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.gender.id_for_label }}">* {{ form.gender.label }}</label>
                 {% render_field form.gender class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.birth_date.id_for_label }}">* {{ form.birth_date.label }}</label>
                 {% render_field form.birth_date class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.birth_place.id_for_label }}">* {{ form.birth_place.label }}</label>
                 {% render_field form.birth_place class='form-control text-uppercase' %}
            </div>
        </section>
        <section class="form-row">
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.level.id_for_label }}">* {{ form.level.label }}</label>
                 {% render_field form.level class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.phone.id_for_label }}">* {{ form.phone.label }}</label>
                 {% render_field form.phone class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <div class="col border rounded py-2 my-3 bg-light">
                    <div>
                        <label for="{{ form.photo.id_for_label }}" >Photo de l'élève</label>
                    </div>
                {% render_field form.photo %}
                </div>
            </div>
        </section>
    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>

<script>
    
    if (typeof(flatpickr) !== "undefined") {
        flatpickr("#id_birth_date", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: true
    });
    }

    hideSubmitButtonIfFormValid()
</script>