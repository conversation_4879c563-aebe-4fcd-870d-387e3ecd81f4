{% load static %}

<div class="container-fluid mt-4">
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            Génération de document en cours
            {% if document_name %} - {{ document_name }}{% endif %}
          </h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-4">
            {% if center %}
              <h4>{{ center }}</h4>
            {% endif %}
            {% if exam %}
              <h5>Examen {{ exam }}</h5>
            {% endif %}
            {% if room_number %}
              <h6>Salle {{ room_number }}</h6>
            {% endif %}
            {% if location %}
              <h6>Localité: {{ location }}</h6>
            {% endif %}
            {% if table_num %}
              <h6>Numéro de table: {{ table_num }}</h6>
            {% endif %}
          </div>

          <div class="alert alert-info">
            <p class="mb-0">
              <i data-feather="info" class="feather-16 mr-2"></i>
              Veuillez patienter pendant la génération du document. Cette opération peut prendre quelques instants.
            </p>
          </div>

          <div class="text-center mt-4">
            <button type="button" class="btn btn-secondary" onclick="$('#celeryProgressModal').modal('show');">
              <i data-feather="eye" class="feather-16 mr-1"></i> Voir la progression
            </button>
            <a hx-get="{% url 'bulletins_list' %}?exam={{exam}}" hx-target="#main-content" class="btn btn-outline-secondary mt-3" id="back"><span data-feather="chevron-left" class="feather-16"></span> Retourner à la liste</a>
            {% if request.user.is_superuser %}
            <a href="{% url 'celery_task_debug' task_id=task_id %}" target="_blank" class="btn btn-outline-info ml-2">
              <i data-feather="code" class="feather-16 mr-1"></i> Debug
            </a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Store the task ID in a hidden input for reference
  document.write('<input type="hidden" id="task_id" value="{{ task_id }}">');

  // Function to initialize everything when the page is ready
  function initializeCeleryTask() {
    console.log("Initializing Celery task...");

    // Initialize feather icons
    if (typeof feather !== 'undefined') {
      feather.replace();
    }

    // Show the progress modal automatically with a slight delay
    setTimeout(function() {
      if (typeof $ !== 'undefined' && typeof $('#celeryProgressModal').modal === 'function') {
        console.log("Showing modal...");
        $('#celeryProgressModal').modal('show');
      } else {
        console.error('jQuery or Bootstrap modal not available');
      }
    }, 500);

    // Initialize the progress bar with a slight delay
    setTimeout(function() {
      if (typeof initCeleryProgressBar === 'function') {
        console.log("Initializing progress bar...");
        initCeleryProgressBar('{{ task_id }}', '{{ download_url_template }}');
      } else {
        console.error('initCeleryProgressBar function not available');
      }
    }, 800);
  }

  // Call the initialization function when the DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeCeleryTask);
  } else {
    // DOM is already ready, call the function directly
    initializeCeleryTask();
  }
</script>
