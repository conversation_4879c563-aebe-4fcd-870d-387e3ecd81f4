<nav class="navbar navbar-expand justify-content-between fixed-top">
    <a class="navbar-brand mb-0 h1 d-none d-md-block" href="{% url 'home' %}">
      <i data-feather="book-open" class="navbar-brand-image d-inline-block align-top mr-2" alt=""></i>
      Gestion des Examens v1.0
    </a>

    <div class="d-flex flex-3 d-block d-md-none ">
      <a class="sidebar-toggle ml-3 text-primary" >
        <i data-feather="menu"></i>
      </a>
    </div>
    <div class="navbar-nav d-flex justify-content-start text-muted">
      <strong>
      {% if user.role == 'EC' %}
        ECOLE {{ user.school|upper|truncatechars:25 }}
      {% elif user.role == 'CL' %}
        COMMISSION LOCALE {{ user.localcommission|upper }}
      {% elif user.role == 'CN' %}
        COMMISSION NATIONALE {{ active_year.short_name }}
      {% elif user.role == 'DG' %}
        RESSOURCES HUMAINES {{ active_year.short_name }}
      {% else %}
        Cherifla - Gestion des Examens {{ active_year.short_name }}
      {% endif %}
      </strong>
    </div>
    <ul class="navbar-nav d-flex justify-content-end mr-2">
      {% if user.is_authenticated %}
        <div class="nav-item dropdown">
          <a class="nav-link avatar-with-name shadow-lg border rounded" id="navbarDropdownMenuLink" data-toggle="dropdown" href="#">
            <i data-feather="user"></i>
          </a>
          <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLink">
            <a class="dropdown-item disabled" href="#">Connecté en tant que ({{ user }})</a>
            <a class="dropdown-item" href="#">Modifier mot de passe</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item text-danger" href="{% url 'logout' %}">Déconnexion</a>
          </div>
        </div>
      {% else %}
      <a class="btn btn-primary text-white" hx-get="{% url 'login' %}" hx-target="#body">
        <i data-feather="lock"></i> Connexion
      </a>
      {% endif %}
    </ul>
  </nav>