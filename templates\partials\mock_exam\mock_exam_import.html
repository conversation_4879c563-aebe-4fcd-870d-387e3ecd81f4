{% load static %}
{% load humanize %}
{% load widget_tweaks %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Examen Blanc</li>
      <li class="breadcrumb-item active" aria-current="page">Importation</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">IMPORTER LES MOYENNES D'EXAMEN BLANC DEPUIS UN FICHIER EXCEL</h5>
  </div>

  <div class="card mb-grid">
    <div class="card-header d-flex justify-content-between align-items-center">
      <div class="card-header-title">Sélectionner une école et un fichier Excel</div>
    </div>
    <div class="card-body">
      <form action="{{ action }}" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <div class="form-row mb-3">
          <div class="col-md-6">
            <label for="school">École</label>
            <select name="school" id="school" class="form-control" required>
              <option value="">Sélectionner une école</option>
              {% for school in schools %}
                <option value="{{ school.id }}">{{ school.name }}</option>
              {% endfor %}
            </select>
          </div>
        </div>
        
        <div class="form-row mb-3">
          <div class="col-md-12">
            <label for="{{ form.excel_file.id_for_label }}">Fichier Excel *</label>
            <div class="custom-file">
              {% render_field form.excel_file class="custom-file-input" %}
              <label class="custom-file-label" for="{{ form.excel_file.id_for_label }}">Choisir un fichier</label>
            </div>
            <small class="form-text text-muted">
              Le fichier doit contenir les colonnes: CODE_ELEVE, MOYENNE_EXAMEN_BLANC
            </small>
          </div>
        </div>
        
        <div class="col-md-12">
          <button type="submit" class="btn btn-primary">
            <span data-feather="upload"></span>
            Importer et vérifier
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
  
  // Display filename when selected
  $('.custom-file-input').on('change', function() {
    var fileName = $(this).val().split('\\').pop();
    $(this).next('.custom-file-label').html(fileName);
  });
</script>
