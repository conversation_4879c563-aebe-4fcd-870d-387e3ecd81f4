{% extends 'components/modal.html' %}

{% block modal_title %}Val<PERSON> la correction{% endblock %}

{% block modal_body %}
    <div class="modal-body">
        <span class="text-muted">Vous êtes sur le point de </span> <span class="font-weight-bold text-success">VALIDER</span>
        <span class="text-muted"> les corrections suivantes:</span> 

        <div class="form-row">
            <div class="text-muted w-50">
                <h5 class="text-muted font-weight-bold">Actuellement</h5>
                <div class="form-group mb-2">
                    <span>{{ correction.initial_last_name_fr }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.initial_first_name_fr }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.initial_full_name_ar }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.get_initial_gender_display }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.initial_birth_date|date:'d/m/Y' }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.initial_birth_place }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.initial_birth_place_ar }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.get_initial_nationality_display }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.get_initial_exam_type_display }}</span>
                </div>
            </div>
            
            <div class="text-primary w-50">
                <h5 class="font-weight-bold text-primary font-weight-bold">Corrections</h5>
                <div class="form-group mb-2">
                    <span>{{ correction.new_last_name_fr|default_if_none:'-----' }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.new_first_name_fr|default_if_none:'-----' }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.new_full_name_ar|default_if_none:'-----' }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.get_new_gender_display|default_if_none:'-----' }}</span>
                </div>
                <div class="form-group mb-2">
                    {% if correction.new_birth_date %}
                    <span>{{correction.new_birth_date|date:'d/m/Y'}}</span>
                    {% else %}
                    <span>-----</span>
                    {% endif %}
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.new_birth_place|default_if_none:'-----' }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.new_birth_place_ar|default_if_none:'-----' }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.get_new_nationality_display|default_if_none:'-----' }}</span>
                </div>
                <div class="form-group mb-2">
                    <span>{{ correction.get_new_exam_type_display|default_if_none:'-----' }}</span>
                </div>

                {% if correction.new_photo %}
                    <div>
                        <img src="{{ correction.new_photo.url }}" 
                          alt="1" class="lazy border img-thumbnail" 
                          height="50px" width="50px">
                    </div>
                {% endif %}
                
            </div>
            
            {% if correction.new_certificate %}
            <div class="" style="width: 800px; height: 800px;">
                <img src="{{ correction.new_certificate.url }}" class="img-fluid lazy" alt="Acte de naissance de l'élève">
            </div>
            {% endif %}

        </div>
        <div class="form-row ml-5">
            <p class="font-weight-bold">
                Confirmer ?
            </p>
        </div>
    </div>
{% endblock %}

{% block modal_footer %}
    <button type="submit" class="btn btn-success">Confirmer</button>
{% endblock %}

