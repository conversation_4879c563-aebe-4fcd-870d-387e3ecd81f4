from import_export.resources import ModelResource, Field
from import_export.widgets import ForeignKeyWidget, Widget
from . import models


class StudentCardResource(ModelResource):
    school = Field(
        column_name='school',
        attribute='school__name',
        default=''
    )
    matricule = Field(
        column_name='matricule',
        attribute='matricule_or_id',
    )
    enrollment_id = Field(
        column_name='id_candidat',
        attribute='enrollment_identifier',
    )
    photo = Field(
        column_name='photo',
        attribute='photo_name',
    )
    drena = Field(
        column_name='drena',
        attribute='get_school_drena',
        default=''
    )
    iepp = Field(
        column_name='iepp',
        attribute='school__iepp',
        default=''
    )
    commission = Field(
        column_name='commission',
        attribute='school__local_commission__location',
        default=''
    )
    statut = Field(
        column_name='statut',
        attribute='get_status_display',
    )
    class Meta:
        model = models.StudentCard
        fields = [
            'matricule', 
            'last_name', 'first_name', 'gender', 'birth_date',
            'birth_place', 'phone', 'level', 'status', 
            'iepp', 'drena', 'commission', 'photo',
        ]
        # use_natural_foreign_keys = True
        export_order = ('matricule', 
            'last_name', 'first_name', 'gender', 'birth_date',
            'birth_place', 'phone', 'level', 'status', 'statut', 
            'school', 'iepp', 'drena', 'commission', 'photo', 
        )
        # Fields that can be imported - these should exactly match the model fields
        import_id_fields = ['id']

        # Specify which fields are allowed to be imported and updated
        import_fields = [
             'status'
        ]

    def get_instance(self, instance_loader, row):
        import_id_fields = self.get_import_id_fields()
        
        # Try each field in our custom preference order
        if 'matricule' in row:
            matricule_value = row['matricule']
            
            # First try matricule_cherifla
            try:
                filter_kwargs = {'matricule_cherifla': matricule_value}
                return self._meta.model.objects.get(**filter_kwargs)
            except self._meta.model.DoesNotExist:
                pass
                
            # Then try matricule_dsps
            try:
                filter_kwargs = {'matricule_dsps': matricule_value}
                return self._meta.model.objects.get(**filter_kwargs)
            except self._meta.model.DoesNotExist:
                pass
                
        # Fall back to default implementation if no match found
        return super().get_instance(instance_loader, row)

    def after_export(self, queryset, dataset, *args, **kwargs):
        """
        Called after exporting data. Uppercase all string values in the dataset, except photo.
        """
        headers = dataset.headers
        for row_index, row in enumerate(dataset._data):
            for col_index, value in enumerate(row):
                # skip photo column
                if headers[col_index] == 'photo':
                    continue
                if isinstance(value, str):
                    dataset._data[row_index][col_index] = value.upper()
        return dataset

    
class StaffResource(ModelResource):
    school = Field(
        column_name='ecole',
        attribute='school__name',
        widget=Widget()
    )
    photo = Field(
        column_name='photo',
        attribute='photo',
        widget=Widget()
    )
    drena = Field(
        column_name='drena',
        attribute='location__name',
        widget=Widget()
    )
    iepp = Field(
        column_name='iepp',
        attribute='school__iepp',
        widget=Widget()
    )
    status = Field(
        column_name='status',
        attribute='get_status_display',
        widget=Widget()
    )
    job = Field(
        column_name='fonction',
        attribute='get_job_display',
        widget=Widget()
    )
    class Meta:
        model = models.Staff
        fields = [
            'code', 'last_name', 'first_name', 
            'birth_date', 'birth_place', 'gender',
            'father', 'mother',
            'date_hired', 'phone', 'address', 'iepp', 'photo'
        ]


class EnrollmentResource(ModelResource):
    school = Field(
        column_name='ecole',
        attribute='school__name',
        widget=Widget()
    )
    matricule = Field(
        column_name='matricule',
        attribute='student__matricule_or_id',
        widget=Widget()
    )
    class Meta:
        model = models.Enrollment
        fields = [
            'student__matricule', 'student__identifier',
             'exam', 'student__last_name', 
            'student__first_name', 'student__birth_date',
            'student__birth_place', 'student__father', 
            'student__mother', 
            'school__local_commission__location', 
            'school', 'confirmed', 'active', 
            'table_num', 'gen_average'
        ]