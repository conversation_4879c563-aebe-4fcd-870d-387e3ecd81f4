{% load widget_tweaks %}


<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <span class="modal-title fw-bold">COMMISSION LOCALE</span>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Informations sur la Commission Locale</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.location.id_for_label }}">Localité *</label>
                {% render_field form.location class='form-control text-uppercase' %}
                <small class="form-text text-muted">Sera utilisé comme nom d'utilisateur de l'espace</small>
                <small class="form-text text-danger" id="#location_exists_el"></small>

            </div>
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.director.id_for_label }}">Nom et Prénoms du représentant *</label>
                 {% render_field form.director class='form-control text-uppercase' %}
            </div>
            
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.phone.id_for_label }}">Contact *</label>
                 {% render_field form.phone class='form-control' %}
                 <small class="form-text text-muted">Sera utilisé comme mot de passe l'espace</small>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <div class="spinner-border d-none" role="status" id="spinner">
            <span class="sr-only">En cours...</span>
        </div>
        <button type="submit" class="btn btn-success" id="submit-btn"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>

<script>
    hideSubmitButtonIfFormValid()
</script>