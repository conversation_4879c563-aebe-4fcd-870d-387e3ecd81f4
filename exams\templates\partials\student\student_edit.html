{% load widget_tweaks %}
<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content" enctype=multipart/form-data>
    {% csrf_token %}
    <div class="modal-header text-success" style="background-color: rgb(152, 225, 197);">
        <span class="modal-title fw-bold">
            CANDIDAT {{ exam|upper }} {{ active_year }}
        </span>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-2 container--fluid">
            <div class="lead text-muted fw-bold"> Identité de l'élève</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-6">
                <label class="text-muted" for="{{ form.candidate_type.id_for_label }}">Candidat <span class="text-danger">*</span></label>
                {% render_field form.exam_type class='form-control'%}
            </div>
            <div class="form-group mb-2 col-lg-6">
                <label class="text-muted" for="{{ form.matricule.id_for_label }}">Matricule National DSPS</label>
                {% render_field form.matricule class='form-control'%}
            </div>

        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-6">
                <label class="text-muted" for="{{ form.last_name.id_for_label }}">Nom <span class="text-danger">*</span></label>
                 {% render_field form.last_name class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-6">
                <label class="text-muted" for="{{ student.first_name.id_for_label }}">Prénoms <span class="text-danger">*</span></label>
                {% render_field form.first_name class='form-control text-uppercase' %}
            </div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ form.gender.id_for_label }}">Genre <span class="text-danger">*</span></label>
                {% render_field form.gender class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ form.full_name_ar.id_for_label }}">Nom et Prénoms en Arabe</label>
                {% render_field form.full_name_ar class='form-control' %}
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ form.birth_date.id_for_label }}">Né(e) le  <span class="text-danger">*</span></label>
                {% render_field form.birth_date class='form-control' %}
            </div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ form.birth_place.id_for_label }}">Lieu de naissance  <span class="text-danger">*</span></label>
                {% render_field form.birth_place class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ form.birth_place_ar.id_for_label }}">Lieu de naissance (en arabe)  <span class="text-danger">*</span></label>
                {% render_field form.birth_place_ar class='form-control' %}
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ enrollment_form.student_phone.id_for_label }}">Contact de l'élève</label>
                {% render_field form.student_phone class='form-control' %}
            </div>
        </div>

        <div class="form-row mt-2">
            <p class="lead fw-bold text-muted">Nationalité et Filliation</p>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ form.nationality.id_for_label }}">Nationalité  <span class="text-danger">*</span></label>
                {% render_field form.nationality class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ enrollment_form.father.id_for_label }}">Père ou tuteur <span class="text-danger">*</span></label>
                {% render_field form.father class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label class="text-muted" for="{{ enrollment_form.mother.id_for_label }}">Mère <span class="text-danger">*</span></label>
                {% render_field form.mother class='form-control text-uppercase' %}
            </div>
        </div>

        <div class="form-row">
            <div class="col border rounded py-2 my-3 bg-light">
                <div>
                    <label class="text-muted" for="{{ form.photo.id_for_label }}" >Photo de l'élève</label>
                </div>
                {% render_field form.photo %}
            </div>
            <div class="img-div col">
                <img src="" id="imgPreview" height="100px" alt="">
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <div class="spinner-border d-none" role="status" id="spinner">
            <span class="sr-only">En cours...</span>
        </div>
        <button type="submit" class="btn btn-success" id="submit-btn"><i data-feather="check"></i> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>

<script>
    feather.replace();
    $('#id_photo').on('change',function(){
        var fileName = $(this).val();
        $(this).next('#photo-label').html(fileName);
    });

    function previewImage(previewInput, that) {
      const file = that.files[0];
      if (file) {
          let reader = new FileReader();
          reader.onload = function (event) {
              $(previewInput).attr("src", event.target.result);
          };
          reader.readAsDataURL(file);
      }
    };

    $("#id_photo").change(function () {
        previewImage("#imgPreview", this)
    });

    try {
        el = document.querySelector("#photo-clear_id");
        if (typeof(el) !== undefined) {
            el.remove()
        }
        el = document.querySelector("label[for=photo-clear_id]");
        if (typeof(el) !== undefined) {
            el.remove()
        }   
    } catch (error) {
        
    }
    
    flatpickr("#id_birth_date", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "true",
        locale: "fr",
    });

    hideSubmitButtonIfFormValid()
</script>