{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">CENTRE D'EXAMEN</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Infos Centre</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.exam.id_for_label }}">Examen *</label>
                {% if not center %}
                    {% render_field form.exam class='form-control mb-0' hx-get='/centres_localite/?uniques=true' hx-target="#location_centers" %}
                {% else %}
                    {% render_field form.exam class='form-control mb-0' %}
                {% endif %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.location.id_for_label }}">Localité *</label>
                {% if not center %}
                 {% render_field form.location class='form-control text-uppercase mb-0' hx-get='/centres_localite/?uniques=true&all_schools=1' hx-target="#location_centers" hx-include="[name=exam]" %}
                {% else %}
                 {% render_field form.location class='form-control text-uppercase mb-0' %}
                {% endif %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6" id="location_centers">
                <label for="{{ form.school.id_for_label }}">Lieu de composition *</label>
                {% render_field form.school class='form-control text-uppercase mb-0' %}
            </div>
            <div class="container px-1 mt-1 mb-3">
                <small class="text-danger" id="center_container"></small>
            </div>

            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.identifier.id_for_label }}">Nom du centre</label>
                 {% render_field form.identifier class='form-control text-uppercase' placeholder='' %}
                <p class="form-text text-muted">
                    Laisser vide pour utiliser le nom de l'école  
                </p>
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.director.id_for_label }}">Directeur du Centre *</label>
                {% render_field form.director class='form-control' %}
            </div>
            <div class="form-group mb-2 col-lg-4 col-md-6">
                <label for="{{ form.phone.id_for_label }}">Contact *</label>
                {% render_field form.phone class='form-control' %}
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <h5>Ecoles déjà sélectionnées</h5>
                <table class="table table-striped table-bordered table-sm">
                    <thead>
                        <tr>
                        <th>ECOLE</th>
                        <th>LOCALITE</th>
                        <th>CANDIDATS</th>
                        </tr>
                    </thead>
                    {% for school in center_schools %}
                        <tr>
                            <td>{{ school }}</td>
                            <td>{{ school.location }}</td>
                            <td>{{ school.count_cepe_candidates|default:'0' }}</td>
                        </tr>
                    {% empty %}
                        <tr><td colspan="3">Aucune école sélectionnée</td></tr>
                    {% endfor %}
                </table>
            </div>
        </div>
        <div class="row">
            <div class="form-group mb-2 col-md-12 mb-3" id="location_schools">
                <label for="{{ form.schools.id_for_label }}">Sélectionner plus écoles</label>
                {% render_field form.schools class='form-control' style='height: 150px !important' %}
            </div>
        </div>

    </div>
    <div class="modal-footer">
        <div class="spinner-border d-none" role="status" id="spinner">
            <span class="sr-only">En cours...</span>
        </div>
        <button type="submit" id="submit-btn" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>
</form>

<script>
    hideSubmitButtonIfFormValid()


    if (typeof(flatpickr) !== "undefined") {
        flatpickr("#id_date", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "True"
    });
    }

    $('.choices').addClass('d-flex align-items-center')
    $('.choices__inner').addClass('bg-white border-0 shadow-sm')

</script>