from django.test import TestCase
from django.contrib.auth import get_user_model
from model_bakery import baker
from project_utils import constants, custom_utils
from .. import models

class SchoolModelTests(TestCase):
    def setUp(self):
        local_commission = baker.make(models.LocalCommission, 
            location='DUEKOUE')
        self.school_name = 'MON ECOLE'
        self.school = baker.make(
            models.School, name=self.school_name, 
            local_commission=local_commission
        )
        self.year = baker.make(models.Year, is_current=True)
        enrollments = baker.make(models.Enrollment, 4,
            school=self.school, year=self.year, active=True, 
            exam=constants.EXAM_CEPE)
        self.user = baker.make(get_user_model(), school=self.school)

    def test_string_representation(self):
        self.assertEqual(
            f'{self.school}', 
            f'{self.school_name} {self.school.local_commission}')

    def test_meta_options(self):
        self.assertEqual(self.school._meta.verbose_name, 'école')
        self.assertEqual(self.school._meta.verbose_name_plural, 'écoles')

    def test_get_candidates_method(self):
        self.client.force_login(self.user)
        expected = models.Enrollment.candidates.get_candidates(
            year=self.year, user=self.user
        )
        self.assertEqual(
            self.school.get_candidates(year=self.year).count(), 
            expected.count())
        
        expected = models.Enrollment.candidates.get_candidates(
            year=self.year, user=self.user, exam=constants.EXAM_CEPE
        ).count()
        self.assertEqual(
            self.school.get_candidates(
                year=self.year, exam=constants.EXAM_CEPE).count(), 
            expected)

    def test_get_exams_fees_method(self):
        school = baker.make(models.School)
        user = get_user_model().objects.create(
            username='test', password='1234', role=constants.ROLE_ECOLE, 
            school=school)
        
        self.client.force_login(user)
        expected = custom_utils.get_exam_fees(
            self.year, exam=constants.EXAM_BEPC, user=user)
        self.assertEqual(
            self.school.get_exam_fees(self.year, constants.EXAM_BEPC, user), 
            expected)
        

class YearModelTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, is_current=True)

    def test_string_representation(self):
        self.assertEqual(f'{self.year}', self.year.name)
    
    def test_meta_options(self):
        self.assertEqual(self.year._meta.verbose_name, 'année scolaire')
        self.assertEqual(
            self.year._meta.verbose_name_plural, 'années scolaires')

    def test_get_price_method(self):
        self.assertEqual(
            self.year.get_price_for(constants.EXAM_CEPE), 
            self.year.price_cepe)
        self.assertEqual(
            self.year.get_price_for(constants.EXAM_BEPC), 
            self.year.price_bepc)
        self.assertEqual(
            self.year.get_price_for(constants.EXAM_BAC), 
            self.year.price_bac)

class StudentModelTests(TestCase):
    def setUp(self):
        self.first_name = 'Saba'
        self.last_name = 'Abdoulaye'
        self.full_name = f'{self.last_name} {self.first_name}'
        year = baker.make(models.Year, is_current=True)
        self.student = baker.make(models.Student, first_name=self.first_name, 
            last_name=self.last_name)
    
    def test_string_representation(self):
        self.assertEqual(f'{self.student}', f'{self.full_name.upper()}')
    
    def test_full_name_method(self):
        # Without argument. Default arg should be constants.LANGUAGE_FRENCH
        self.assertEqual(f'{self.student.get_full_name()}', self.full_name.upper())

        # With arguments
        returned = self.student.get_full_name(constants.LANGUAGE_FRENCH)
        self.assertEqual(f'{returned}', self.full_name.upper())
        
        returned = self.student.get_full_name(constants.LANGUAGE_ARABIC)
        self.assertEqual(f'{returned}', '')

    def test_verbose_names(self):
        self.assertEqual(self.student._meta.verbose_name, 'élève')
        self.assertEqual(self.student._meta.verbose_name_plural, 'élèves')


class EnrollmentModelTests(TestCase):
    def setUp(self):
        self.school = baker.make(models.School)
        self.user = baker.make(get_user_model(), school=self.school)
        self.year = baker.make(models.Year, is_current=True)
        self.student = baker.make(models.Student, first_name='Abdoulaye', 
            last_name='Saba')
        self.enrollment = baker.make(models.Enrollment, student=self.student, 
            school=self.school, year=self.year)
        self.enrollments = baker.make(models.Enrollment, 4, confirmed=True, 
            school=self.school, year=self.year)
        self.client.force_login(self.user)

    def test_string_representation(self):
        self.assertEqual(f'{self.enrollment}', f'{self.student}')

    def test_default_values(self):
        self.assertTrue(self.enrollment.active)
        self.assertFalse(self.enrollment.confirmed)

    def test_get_candidates_method_for_active_students(self):
        active_students = models.Enrollment.objects.filter(
            school=self.school,
            year=self.year,
            active=True
        )
        self.assertEqual(
            models.Enrollment.candidates.get_candidates(
                year=self.year, confirmed=False, user=self.user).count(),
            active_students.count()
        )

    def test_get_candidates_method_for_confirmed_students(self):
        active_students = models.Enrollment.objects.filter(
            school=self.school,
            year=self.year,
            active=True,
            confirmed=True
        )
        self.assertEqual(
            models.Enrollment.candidates.get_candidates(
                year=self.year, user=self.user).count(),
            active_students.count()
        )

    def test_get_candidates_method_for_all_students(self):
        all_students = models.Enrollment.objects.filter(
            school=self.school,
            year=self.year
        )
        self.assertEqual(
            models.Enrollment.candidates.get_candidates(
                year=self.year, user=self.user, 
                confirmed=False).count(),
            all_students.count()
        )


class LocalCommissionManagerTestCase(TestCase):
    def setUp(self):
        # Create a user and a local commission using model_bakery
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_LOCALE)

    def test_get_active_commissions(self):
        # Test that the `get_active_commissions` method returns only active commissions
        baker.make(models.LocalCommission, active=False, _quantity=3)
        active_commissions = baker.make(
            models.LocalCommission, active=True, _quantity=2)
        queryset = models.LocalCommission.objects.get_active_commissions()
        self.assertEqual(set(queryset), set(active_commissions))

    def test_get_schools_count(self):
        # Test that the `get_schools_count` method returns the correct number of schools
        local_commission = baker.make(models.LocalCommission)
        baker.make(models.School, local_commission=local_commission, 
                   _quantity=3)
        baker.make(models.School, _quantity=2)
        schools_count = local_commission.get_schools_count()
        self.assertEqual(schools_count, 3)


class LocalCommissionModelTestCase(TestCase):
    def test_str(self):
        # Test the __str__ method returns the location string representation
        local_commission = baker.make(models.LocalCommission, 
            location='Paris')
        self.assertEqual(str(local_commission), 'Paris')

    def test_get_full_name(self):
        # Test the get_full_name method returns the full name of the user
        user = baker.make(
            get_user_model(), first_name='John', 
            last_name='Doe', role=constants.ROLE_COMMISSION_LOCALE)
        local_commission = baker.make(models.LocalCommission, user=user)
        self.assertEqual(local_commission.get_full_name(), 'Doe John')

    def test_unique_together_constraint(self):
        # Test the unique_together constraint on the location and user fields
        user = baker.make(get_user_model(), role=constants.ROLE_COMMISSION_LOCALE)
        baker.make(models.LocalCommission, location='Paris', user=user)
        with self.assertRaises(Exception):
            baker.make(models.LocalCommission, location='Paris', user=user)


class PaymentManagerTestCase(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, name='2022')
        self.local_commission = baker.make(models.LocalCommission)

        self.school = baker.make(
            models.School, name='Test School', 
            local_commission=self.local_commission)
        
        self.school2 = baker.make(
            models.School, name='Another school', 
            local_commission=self.local_commission)
                
        self.user_commission_locale = baker.make(get_user_model(), 
            username='commission_locale', password='password', 
            role=constants.ROLE_COMMISSION_LOCALE, 
            localcommission=self.local_commission)
        
        self.user_ecole = baker.make(get_user_model(), 
            username='ecole', password='password', 
            role=constants.ROLE_ECOLE, school=self.school)
        
        self.another_user_ecole = baker.make(get_user_model(), 
            username='another_user', password='password', 
            role=constants.ROLE_ECOLE)
        
        self.payment1 = baker.make(models.SchoolPayment, amount=100, 
            date='2022-01-01', school=self.school, year=self.year)
        self.payment2 = baker.make(models.SchoolPayment, amount=200, 
            date='2022-02-01', school=self.school, year=self.year)
        self.payment3 = baker.make(models.SchoolPayment, amount=200, 
            date='2022-02-01', school=self.school2, year=self.year)
    
    def test_get_queryset_year_filter(self):
        queryset = models.SchoolPayment.objects.get_queryset(year=self.year)
        self.assertIn(self.payment1, queryset)
        self.assertIn(self.payment2, queryset)
        self.assertIn(self.payment3, queryset)
    
    def test_get_queryset_user_ecole_filter(self):
        queryset = models.SchoolPayment.objects.get_queryset(
            user=self.user_ecole, year=self.year)
        self.assertEqual(queryset.count(), 2)
    
    def test_get_queryset_user_commission_locale_filter(self):
        queryset = models.SchoolPayment.objects.get_queryset(
            user=self.user_commission_locale, year=self.year)
        self.assertEqual(queryset.count(), 3)
    
    def test_get_total(self):
        total = models.SchoolPayment.objects.get_total(user=None, year=self.year)
        self.assertEqual(total, 500)
        
        total = models.SchoolPayment.objects.get_total(
            user=self.user_ecole, year=self.year)
        self.assertEqual(total, 300)