from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from formtools.wizard.views import SessionWizardView
from project_utils import constants, custom_utils
from . import forms, models


def is_school_stats_completed(school, year):
    """Helper method to check if school statistics are completed for a given year"""
    return models.SchoolStatistics.objects.filter(
        school=school,
        year=year,
        stats_completed=True
    ).exists()


class SchoolInformationWizardView(LoginRequiredMixin, SessionWizardView):
    """Multi-step wizard for school information and statistics"""
    
    template_name = 'exams/school_information_wizard.html'
    form_list = [
        ('school_info', forms.SchoolInformationForm),
        ('school_stats', forms.SchoolStatisticsForm),
    ]
    
    def dispatch(self, request, *args, **kwargs):
        # Check if user is a school
        if request.user.role != constants.ROLE_ECOLE:
            messages.error(request, "Accès non autorisé.")
            return redirect('home')
        
        # Check if user has a school
        if not request.user.school:
            messages.error(request, "Aucune école associée à votre compte.")
            return redirect('home')
        
        # Check if information is already updated
        school = request.user.school
        current_year = custom_utils.get_selected_year(request)
        stats_completed = is_school_stats_completed(school, current_year)

        # Check if this school is required to provide stats
        school_needs_stats = (not current_year.stats_required_schools.exists() or
                             school in current_year.stats_required_schools.all())

        # Only redirect away if everything is complete AND stats are required for this school
        if school.information_updated and stats_completed and school_needs_stats:
            messages.info(request, "Les informations de votre école sont déjà à jour.")
            return redirect('school_information_check')

        # If stats are not required for this school, redirect away
        if not school_needs_stats:
            messages.info(request, "Votre école n'est pas tenue de fournir des statistiques cette année.")
            return redirect('home')
        
        return super().dispatch(request, *args, **kwargs)
    
    def get_form_instance(self, step):
        """Get the instance for each form step"""
        if step == 'school_info':
            return self.request.user.school
        elif step == 'school_stats':
            # Try to get existing statistics for current year
            current_year = custom_utils.get_selected_year(self.request)
            try:
                return models.SchoolStatistics.objects.get(
                    school=self.request.user.school,
                    year=current_year
                )
            except models.SchoolStatistics.DoesNotExist:
                return None
        return None
    
    def get_context_data(self, form, **kwargs):
        context = super().get_context_data(form=form, **kwargs)
        context['school'] = self.request.user.school
        context['current_step'] = self.steps.current
        context['step_count'] = self.steps.count
        context['step_index'] = self.steps.index

        # Calculate progress percentage
        context['progress_percentage'] = int(((self.steps.index + 1) / self.steps.count) * 100)

        # Add step titles
        step_titles = {
            'school_info': 'Informations de l\'école',
            'school_stats': 'Statistiques de l\'école'
        }
        context['step_title'] = step_titles.get(self.steps.current, '')

        return context
    
    def done(self, form_list, **kwargs):
        """Process the completed wizard"""
        school_info_form = form_list[0]
        school_stats_form = form_list[1]
        
        # Save school information
        school = school_info_form.save(commit=False)
        school.information_updated = True
        school.save()
        
        # Save school statistics
        stats = school_stats_form.save(commit=False)
        stats.school = school
        stats.year = custom_utils.get_selected_year(self.request)
        stats.stats_completed = True
        stats.save()
        
        messages.success(
            self.request,
            "Les informations de votre école ont été mises à jour avec succès!"
        )

        return redirect('school_information_check')


@login_required
def school_information_check_view(request):
    """Check if school information needs to be updated"""
    
    # Check if user is a school
    if request.user.role != constants.ROLE_ECOLE:
        messages.error(request, "Accès non autorisé.")
        return redirect('home')
    
    # Check if user has a school
    if not request.user.school:
        messages.error(request, "Aucune école associée à votre compte.")
        return redirect('home')
    
    school = request.user.school
    current_year = custom_utils.get_selected_year(request)

    # Check if school fees record exists
    has_fees_record = False
    try:
        models.SchoolFees.objects.get(school=school)
        has_fees_record = True
    except models.SchoolFees.DoesNotExist:
        has_fees_record = False

    # Check if stats are completed using helper method
    stats_completed = is_school_stats_completed(school, current_year)

    # Check if this school is required to provide stats
    school_needs_stats = (not current_year.stats_required_schools.exists() or
                         school in current_year.stats_required_schools.all())

    # If stats are not required for this school, redirect to home
    if not school_needs_stats:
        return redirect('home')

    # If everything is complete, redirect to home
    if school.information_updated and stats_completed and has_fees_record:
        return redirect('home')

    # Determine the current step
    if not school.information_updated or not stats_completed:
        # Information/stats step
        step = 'information'
    else:
        # Payment step
        step = 'payment'

    # Show the information update required page
    context = {
        'school': school,
        'information_updated': school.information_updated,
        'stats_updated': stats_completed,
        'has_fees_record': has_fees_record,
        'step': step,
    }
    
    return render(request, 'exams/school_information_required.html', context)
