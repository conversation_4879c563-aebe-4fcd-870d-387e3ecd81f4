{% load static %}
<!-- Celery Progress Modal -->
<div class="modal fade" id="celeryProgressModal" tabindex="-1" role="dialog" aria-labelledby="celeryProgressModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="celeryProgressModalLabel">Génération du document en cours</h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" id="celery-close-button" style="display: none;">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="progress mb-3" style="height: 25px;">
          <div id="celery-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">0%</div>
        </div>
        <p id="celery-progress-message" class="text-center font-weight-bold">Préparation...</p>
        <div class="text-center mb-3">
          <div class="spinner-border text-primary" role="status" id="celery-spinner">
            <span class="sr-only">Chargement...</span>
          </div>
        </div>
        <div id="celery-result-container" class="text-center mt-4" style="display: none;">
          <div class="alert alert-success">
            <i data-feather="check-circle" class="feather-16 mr-2"></i>
            Document généré avec succès!
          </div>
          <a id="celery-download-link" href="#" class="btn btn-success btn-lg">
            <i data-feather="download" class="feather-16 mr-1"></i> Télécharger le document
          </a>
          <button type="button" class="btn btn-secondary mt-2" data-dismiss="modal">
            <i data-feather="x" class="feather-16 mr-1"></i> Fermer
          </button>
        </div>
        <div id="celery-error-container" class="text-center mt-4" style="display: none;">
          <div class="alert alert-danger">
            <i data-feather="alert-triangle" class="feather-16 mr-2"></i>
            <span id="celery-error-message">Une erreur est survenue</span>
          </div>
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i data-feather="x" class="feather-16 mr-1"></i> Fermer
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Celery Progress JavaScript -->
<script>
  // Function to initialize the progress bar
  function initCeleryProgressBar(taskId, downloadUrlTemplate) {
    // Show the modal
    $('#celeryProgressModal').modal('show');

    // Reset the progress bar and message
    $('#celery-progress-bar').css('width', '0%').attr('aria-valuenow', 0).text('0%');
    $('#celery-progress-message').text('Préparation...');
    $('#celery-result-container').hide();
    $('#celery-error-container').hide();
    $('#celery-spinner').show();
    $('#celery-close-button').hide();

    // Initialize the progress bar
    const progressUrl = `/celery-progress/${taskId}/`;

    CeleryProgressBar.initProgressBar(progressUrl, {
      onProgress: function(progressBarElement, progressBarMessageElement, progress) {
        // Update the progress bar
        $('#celery-progress-bar').css('width', progress.percent + '%').attr('aria-valuenow', progress.percent);
        $('#celery-progress-bar').text(progress.percent + '%');
        console.log('Progress:', progress.percent + '%', $('#celery-progress-bar'));

        // Update the message if provided
        if (progress.description) {
          $('#celery-progress-message').text(progress.description);
        }
      },
      onSuccess: function(progressBarElement, progressBarMessageElement, result) {
        // Check if there's an error in the result
        if (result.error) {
          // Handle error
          $('#celery-progress-bar').css('width', '100%').attr('aria-valuenow', 100).text('Erreur');
          $('#celery-progress-bar').removeClass('progress-bar-animated').addClass('bg-danger');

          // Hide the spinner
          $('#celery-spinner').hide();

          // Show the close button
          $('#celery-close-button').show();

          // Update the message
          $('#celery-progress-message').text('Une erreur est survenue');

          // Show the error container
          $('#celery-error-container').show();
          $('#celery-error-message').text(result.error);

          console.error('Task error:', result.error);

          // Initialize feather icons
          if (typeof feather !== 'undefined') {
            feather.replace();
          }
        } else {
          // Update the progress bar to 100%
          $('#celery-progress-bar').css('width', '100%').attr('aria-valuenow', 100).text('100%');
          $('#celery-progress-bar').removeClass('progress-bar-animated').addClass('bg-success');

          // Hide the spinner
          $('#celery-spinner').hide();

          // Show the close button
          $('#celery-close-button').show();

          // Update the message
          $('#celery-progress-message').text('Document généré avec succès!');

          // Show the download button
          $('#celery-result-container').show();

          // Set the download link
          const downloadUrl = downloadUrlTemplate.replace('FILENAME_PLACEHOLDER', result.filename);
          $('#celery-download-link').attr('href', downloadUrl);

          // Initialize feather icons
          if (typeof feather !== 'undefined') {
            feather.replace();
          }
        }
      },
      onError: function(progressBarElement, progressBarMessageElement, error) {
        // Update the progress bar to show error
        $('#celery-progress-bar').css('width', '100%').attr('aria-valuenow', 100).text('Erreur');
        $('#celery-progress-bar').removeClass('progress-bar-animated').addClass('bg-danger');

        // Hide the spinner
        $('#celery-spinner').hide();

        // Show the close button
        $('#celery-close-button').show();

        // Update the message
        $('#celery-progress-message').text('Une erreur est survenue');

        // Show the error container
        $('#celery-error-container').show();
        $('#celery-error-message').text(error || 'Une erreur inconnue est survenue');

        // Initialize feather icons
        if (typeof feather !== 'undefined') {
          feather.replace();
        }
      }
    });
  }

  // Function to close the modal
  function closeCeleryProgressModal() {
    $('#celeryProgressModal').modal('hide');
  }
</script>
