from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm, AuthenticationForm
from .models import CustomUser
from exams.models import Year


class CustomAuthenticationForm(AuthenticationForm):
    """Custom authentication form with school year selection"""
    school_year = forms.ModelChoiceField(
        queryset=Year.objects.all(),
        empty_label="Sélectionner une année scolaire",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="ANNÉE SCOLAIRE",
        required=True
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Order years by most recent first, with current year at the top
        self.fields['school_year'].queryset = Year.objects.order_by('-is_current', '-short_name')

        # Set default to current year if it exists
        try:
            current_year = Year.objects.filter(is_current=True).first()
            if current_year:
                self.fields['school_year'].initial = current_year
        except Year.DoesNotExist:
            pass


class CustomUserCreationForm(UserCreationForm):
    class Meta(UserCreationForm.Meta):
        model = CustomUser
        fields = UserCreationForm.Meta.fields #+ ('school',)


class CustomUserChangeForm(UserChangeForm):
    class Meta:
        model = CustomUser
        fields = UserChangeForm.Meta.fields