<div class="container d-flex flex-column align-items-center text-dark">
{% if enrollment %}
    {% if enrollment.student.photo %}
    <img src="{{ enrollment.student.photo.url }}" alt="" 
        class="border rounded-circle" height="100px" width="100px">
    {% endif %}
    <h5><strong>{{ enrollment }}</strong></h5>
    <h5>Examen: {{ enrollment.get_exam_display }} <strong>({{ enrollment.get_exam_type_display }})</strong></h5>
    <span>Née le <strong>{{ enrollment.student.birth_date|date:'d/m/Y' }}</strong>
        à <strong>{{ enrollment.student.birth_place }}</strong>
    </span>
    <span>Centre de composition <strong>{{ enrollment.center }}</strong></span>
    <span>Points obtenu <strong>{{ enrollment.total|floatformat:0 }}</strong></span>
    <span>Moyenne obtenu <strong>{{ enrollment.average|floatformat:2 }} / 20</strong></span>
    <span>Sexe <strong>{{ enrollment.student.get_gender_display }}</strong></span>
    <h5 class="font-weight-bold">Résultat: <span class="badge {% if enrollment.average >= 10 %} badge-success {% else %} badge-danger {% endif %}">
        {% if enrollment.average >= 10 %} FELICITATIONS, VOUS ETES ADMIS {% else %} DESOLE. VOUS N'ÊTES PAS ADMIS {% endif %}
    </span></h5>
{% else %}
    <p class="text-danger">Désolé. Aucun candidat ne correspond au numéro entré.</p>
{% endif %}
    <div class="my-3">
        <a class="btn btn-primary" 
           href="" hx-get="{% url 'candidate_space' %}" 
           hx-target="#body"> <span data-feather="dollar">
        Effectuer une nouvelle vérification</a>

    </div>
</div>