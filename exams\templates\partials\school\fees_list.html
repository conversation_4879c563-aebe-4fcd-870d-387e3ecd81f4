{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Ecoles</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      LISTE DES PAIEMENTS DES COTISATIONS ANNUELLES
      {% if user.role == ROLE_COMMISSION_LOCALE %} DE {{ user.localcommission }} {% endif %}  
      ({{ payments_total|intcomma }} F) 
    </h5>
  </div>

  {% if perms.exams.add_schoolpayment %}
    <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
      <div>
        <a class="btn btn-success text-white"
           hx-get="{% url 'school_fees_add' %}"
           hx-target="#dialog">
           + Nouvelle cotisation</a>
      </div>

      <div class="dropdown">
        <button class="btn btn-warning dropdown" type="button" 
                data-toggle="dropdown" aria-expanded="false">
          <i data-feather="file-text"></i> Imprimer
        </button>
        <div class="dropdown-menu">
            <a class="dropdown-item" 
              href="{% url 'fees_pdf' %}">Liste cotisations </a>
          </div>
      </div>
    </div>
  {% endif %}

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                  {% if user.role != ROLE_ECOLE %}
                    <th scope="col" class="text-white">LOCALITE</th>
                  {% endif %}
                <th scope="col" class="text-white">ECOLE</th>
                <th scope="col" class="text-white">MONTANT</th>
                <th scope="col" class="text-white">DATE</th>
                <th scope="col" class="text-white">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for fee in fees %}
              <tr>
                {% if user.role != ROLE_ECOLE %}
                <td>{{ fee.school.local_commission }}</td>
                {% endif %}
                <td>{{ fee.school }}</td>
                <td>{{ fee.amount }}</td>
                <td>{{ fee.date|date:'d/m/Y' }}</td>
                <td>
                  <button class="btn btn-primary btn-sm dropdown" type="button" data-toggle="dropdown" aria-expanded="false">
                    <i data-feather="more-horizontal"></i>
                    </button>
                    <div class="dropdown-menu">
                    {% if perms.exams.change_schoolfees %}
                    <a href="" class="dropdown-item btn btn-sm {% if school.confirmed %} disabled {% endif %}"
                    hx-get="{% url 'school_fees_edit' fee.id %}"
                    hx-target="#dialog"> <span data-feather="edit"></span>Modifier</a>
                    {% endif %}
                    </div>
                </td>
                
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>