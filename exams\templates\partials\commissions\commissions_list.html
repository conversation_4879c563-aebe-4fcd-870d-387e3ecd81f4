{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Commissions Locales</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">LISTE DES COMMISSIONS LOCALES</h5>
  </div>

  {% if perms.exams.add_localcommission %}
    <div class="container-fluid p-0 mb-3">
      <a class="btn btn-success text-white"
         hx-get="{% url 'commission_add' %}"
         hx-target="#dialog"> 
         + Ajouter </a>
    </div>
  {% endif %}

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-actions table-striped table-hover mb-0" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">LOCALITE</th>
                <th scope="col" class="text-white">RESPONSABLE</th>
                <th scope="col" class="text-white">NOM D'UTILISATEUR</th>
                <th scope="col" class="text-white">MOT DE PASSE</th>
                <th scope="col" class="text-white">STATUT</th>
                <th scope="col" class="text-white">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for commission in commissions %}
              <tr>
                <td>{{ commission.location }}</td>
                <td>{{ commission.get_full_name }}</td>
                <td>{{ commission.user.username }}</td>
                <td>{{ commission.initial_password }}</td>
                <td>
                  {% if commission.active %} 
                  <span class="badge badge-pill badge-success">ACTIF</span>
                  {% else %}  
                    <span class="badge badge-pill badge-warning">INACTIF</span>
                  {% endif %}
                </td>
                  <td>
                    <div class="dropdown">
                      <a class="btn btn-sm border" href="#"
                    hx-get="{% url 'commission_edit' commission.id %}"
                    hx-target="#dialog"> <span data-feather='edit'></span> </a>
                      <button class="btn btn-primary btn-sm dropdown" type="button" data-toggle="dropdown" aria-expanded="false">
                      <i data-feather="more-horizontal"></i>
                      </button>
                      <div class="dropdown-menu">
                        <a class="dropdown-item" href="{% url 'center_refresh' commission.id 'cepe' %}">
                            Actu centres CEPE
                          </a>
                        <a class="dropdown-item" 
                             href="">
                            Actu centres BEPC
                          </a>
                        <a class="dropdown-item" 
                             href="">
                            Actu centres BAC
                          </a>
                      </div>
                    </div>
                  </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready( function() {
    feather.replace();
    $('#datatable').DataTable({});
});
</script>
