{% load widget_tweaks %}
<form action="{{ request.path }}?school={{ enrollment.school_id }}" method="post" class="container-fluid mt-2" hx-post="{{ request.path }}?school={{ enrollment.school_id }}&one_only={{ request.GET.one_only }}" hx-target="#main-content">
    {% csrf_token %}
    <p class="font-weight-bold">MOYENNE EXAMEN BLANC / {{ request.GET.exam|upper }} </p>
    <hr>
    <p class="font-weight-bold">{{ stats.students_marked|default:'0' }} / {{ stats.students_count }} Notés</p>
    <hr>
    <div class="form-row">
        <div class="form-group my-2 col-6">
            <label for="{{ form.student.id_for_label}}">Elève</label>
            {% render_field form.student class='form-control' readonly='readonly' %}
        </div>
        <div class="form-group my-2 col-6">
            <label for="{{ form.school.id_for_label}}">Ecole</label>
            {% render_field form.school class='form-control' readonly='readonly' %}
        </div>
    </div>
    <div class="form-row">
        <div class="form-group my-2 col-md-6">
            {% render_field form.mock_average class='form-control' max='20' autofocus='autofocus' onfocus='let temp = this.value; if (this.value == 0) {this.value = 0} else {this.value=0; this.value = temp}' %}
            <div class="text-danger">{{ form.mock_average.errors }}</div>
        </div>
    </div>
    <div class="form-row mt-2">
        <div class="form-group col-md-2">
            <button type="submit" class="btn btn-success btn-block"> Enregistrer</button>
        </div>
    </div>
</form>