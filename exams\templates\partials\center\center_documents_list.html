<div class="container mt-2 mb-5 mx-auto">
  <!-- Search Filter -->
  <div class="row mb-4">
    <div class="col-md-6 offset-md-3">
      <div class="input-group">
        <div class="input-group-prepend">
          <span class="input-group-text bg-primary text-white">
            <i data-feather="search" class="feather-16"></i>
          </span>
        </div>
        <input type="text" id="centerSearchInput" class="form-control" placeholder="Rechercher un centre..." aria-label="Rechercher un centre">
      </div>
    </div>
  </div>

  <!-- No results message -->
  <div id="noResultsMessage" class="row mb-4" style="display: none;">
    <div class="col-md-6 offset-md-3">
      <div class="alert alert-info text-center">
        <i data-feather="info" class="feather-16 mr-2"></i>
        Aucun centre ne correspond à votre recherche.
        <button class="btn btn-sm btn-outline-primary ml-2" id="resetSearchBtn">
          Réinitialiser
        </button>
      </div>
    </div>
  </div>

  <!-- Results counter -->
  <div id="resultsCounter" class="row mb-3" style="display: none;">
    <div class="col-md-6 offset-md-3">
      <div class="text-center text-muted">
        <span id="visibleCentersCount">0</span> centres affichés sur <span id="totalCentersCount">0</span>
      </div>
    </div>
  </div>

  <div class="row">
  <div class="card col-md-4">
    <div class="card-header text-success">
      <strong>CONVOCATIONS</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
      <div class="btn-group my-1">
        <a class="btn btn-sm btn-success btn-block text-white {% if not center.complete %} disabled {% endif %}"
          hx-get="{% url 'center_candidates_async' center.id exam %}?document=convocations"
          hx-target="#main-content">{{ center }}</a>
        <button type="button" class="btn btn-sm btn-success dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false" data-reference="parent" {% if not center.complete %} disabled {% endif %}>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div class="dropdown-menu">
          {% for room in center.room_set.all %}
          <a class="dropdown-item"
            hx-get="{% url 'center_candidates_async' center.id exam %}?document=convocations&salle={{room.number}}"
            hx-target="#main-content">{{ room }}</a>
          {% endfor %}
        </div>
      </div>
      {% endfor %}
      <div>
        <small class="text-muted">Les flèches permettent d'imprimer par salle.</small><br>
        <small class="text-info"><i data-feather="info" class="feather-12"></i> Traitement en arrière-plan.</small>
      </div>
    </div>
  </div>
  <div class="card col-md-4">
    <div class="card-header">
      <strong>FICHES DE TABLE</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
      <div class="btn-group my-1">
        <a class="btn btn-sm btn-warning btn-block {% if not center.complete %} disabled {% endif %}"
          hx-get="{% url 'center_candidates_async' center.id exam %}?document=fiches_table"
          hx-target="#main-content">{{ center }}</a>
        <button type="button" class="btn btn-sm btn-warning dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false" data-reference="parent">
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div class="dropdown-menu">
          {% for room in center.room_set.all %}
          <a class="dropdown-item"
            hx-get="{% url 'center_candidates_async' center.id exam %}?document=fiches_table&salle={{room.number}}"
            hx-target="#main-content">{{ room }}</a>
          {% endfor %}
        </div>
      </div>
      {% endfor %}
      <div>
        <small class="text-muted">Les flèches permettent d'imprimer par salle.</small><br>
        <small class="text-info"><i data-feather="info" class="feather-12"></i> Traitement en arrière-plan.</small>
      </div>
    </div>
  </div>
  <div class="card col-md-4">
    <div class="card-header text-success">
      <strong>FICHES D'EMARGEMENT</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
      <div class="btn-group my-1">
        <a class="btn btn-sm btn-outline-success btn-block {% if not center.complete %} disabled {% endif %}"
          href="{% url 'center_candidates' center.id exam %}?document=fiches_emargement">{{ center }}</a>
        <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-expanded="false" data-reference="parent">
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div class="dropdown-menu">
          {% for room in center.room_set.all %}
          <a class="dropdown-item"
            href="{% url 'center_candidates' center.id exam %}?document=fiches_emargement&salle={{room.number}}">{{ room }}</a>
          {% endfor %}
        </div>
      </div>
      {% endfor %}
      <div>
        <small class="text-muted">Les flèches permettent d'imprimer par salle.</small><br>
      </div>
    </div>
  </div>
  <div class="card col-md-4">
    <div class="card-header text-info">
      <strong>LISTES PAR CENTRE</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
        <a href="{% url 'center_candidates' center.id exam %}"
           class="btn btn-info btn-sm my-1"
           style="min-width: auto">{{ center }}</a>
      {% endfor %}
    </div>
  </div>
  <div class="card col-md-4">
    <div class="card-header text-danger">
      <strong>LISTES PAR SALLE</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
        <a href="{% url 'center_rooms' center.id exam %}"
           class="btn btn-danger btn-sm my-1"
           style="min-width: auto">{{ center }}</a>
      {% endfor %}
    </div>
  </div>
  <div class="card col-md-4">
    <div class="card-header text-info">
      <strong>FICHES ANONYMAT</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
        <a href="{% url 'center_candidates' center.id exam %}?document=anonymat"
           class="btn btn-light border-primary btn-sm my-1"
           style="min-width: auto">{{ center }}</a>
      {% endfor %}
    </div>
  </div>
  <div class="card col-md-4">
    <div class="card-header">
      <strong>FICHES REPORT ORAL</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
        <a href="{% url 'center_candidates' center.id exam %}?document=fiche_oral"
           class="btn btn-dark btn-sm my-1"
           style="min-width: auto">{{ center }}</a>
      {% endfor %}
    </div>
  </div>
  <div class="card col-md-4">
    <div class="card-header text-success">
      <strong>FICHES REPORT ECRIT</strong>
    </div>
    <div class="card-body container-fluid d-flex flex-column">
      {% for center in centers %}
        <a href="{% url 'center_candidates' center.id exam %}?document=fiche_ecrit"
           class="btn btn-success btn-sm my-1"
           style="min-width: auto">{{ center }}</a>
      {% endfor %}
    </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    // Initialize Feather icons
    if (typeof feather !== 'undefined') {
      feather.replace();
    }

    // Count total centers
    var totalCenters = countTotalCenters();
    $('#totalCentersCount').text(totalCenters);

    // Center search functionality
    $('#centerSearchInput').on('keyup', function() {
      var searchText = $(this).val().toLowerCase();
      filterCenters(searchText);
    });

    // Clear search button
    $('#clearSearchBtn').on('click', function() {
      $('#centerSearchInput').val('');
      filterCenters('');
    });

    // Reset search button (in no results message)
    $('#resetSearchBtn').on('click', function() {
      $('#centerSearchInput').val('');
      filterCenters('');
    });

    // Function to count total centers
    function countTotalCenters() {
      var uniqueCenters = new Set();

      // Count centers in button groups
      $('.btn-group a.btn-block').each(function() {
        uniqueCenters.add($(this).text().trim());
      });

      // Count centers in simple buttons
      $('.card a.btn-sm').each(function() {
        uniqueCenters.add($(this).text().trim());
      });

      return uniqueCenters.size;
    }

    // Function to filter centers
    function filterCenters(searchText) {
      // If search is empty, show all centers and hide no results message
      if (searchText === '') {
        $('.btn-group, .card a.btn-sm').show();
        $('.card').show();
        $('#noResultsMessage').hide();
        $('#resultsCounter').hide();
        return;
      }

      // Hide all centers first
      $('.btn-group, .card a.btn-sm').hide();

      // Show centers that match the search text
      $('.btn-group a:contains("' + searchText + '"), .card a.btn-sm:contains("' + searchText + '")').each(function() {
        $(this).show();
        $(this).closest('.btn-group').show();
      });

      // Check if any centers are visible in each card
      var totalVisibleItems = 0;
      var uniqueVisibleCenters = new Set();

      $('.card').each(function() {
        var card = $(this);
        var visibleItems = card.find('.btn-group:visible, a.btn-sm:visible').length;
        totalVisibleItems += visibleItems;

        // Count unique visible centers
        card.find('.btn-group a.btn-block:visible').each(function() {
          uniqueVisibleCenters.add($(this).text().trim());
        });

        card.find('a.btn-sm:visible').each(function() {
          uniqueVisibleCenters.add($(this).text().trim());
        });

        if (visibleItems > 0) {
          card.show();
        } else {
          card.hide();
        }
      });

      // Update the counter
      var visibleCentersCount = uniqueVisibleCenters.size;
      $('#visibleCentersCount').text(visibleCentersCount);

      // Show/hide no results message and counter
      if (totalVisibleItems === 0) {
        $('#noResultsMessage').show();
        $('#resultsCounter').hide();
        // Reinitialize feather icons for the message
        if (typeof feather !== 'undefined') {
          feather.replace();
        }
      } else {
        $('#noResultsMessage').hide();
        $('#resultsCounter').show();
      }
    }

    // Case-insensitive contains selector
    $.expr[':'].contains = function(a, i, m) {
      return $(a).text().toLowerCase().indexOf(m[3].toLowerCase()) >= 0;
    };
  });
</script>