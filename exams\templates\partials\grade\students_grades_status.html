{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'grades' %}?exam={{exam}}" 
           hx-target="#main-content" hx-push-url="{% url 'grades' %}?exam={{exam}}">Notes</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Statut des notes par élève</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">STATUT DES NOTES PAR ÉLÈVE</h5>
  </div>
  
  <!-- Center Selection -->
  <div class="row d-flex justify-content-center my-2">
    <div class="wrapper col-md-6">
      Centre:
      <select name="center" id="center" 
        class="form-control" hx-get="{% url 'students_grades_status' %}" 
        hx-target="#main-content" hx-include="[name=search], [name=per_page]">
        <option value="0" {% if not selected_center %} selected="selected" {% endif %}>Sélectionner un centre</option>
        {% for center in centers_list %}
          <option value="{{ center.id }}" {% if selected_center == center.id %} selected="selected" {% endif %}>{{ center }}</option>
        {% empty %}
          <option value="0" disabled>Aucun centre disponible</option>
        {% endfor %}
      </select>
    </div>
  </div>
  
  {% if selected_center %}
    {% include 'components/search_and_page_size.html' with include_items='[name=center]' %}
    <div class="row" id="table-wrapper">
      <div class="col">
        <div class="card mb-grid">
          <div class="table-responsive-md">
            <table class="table table-sm table-actions table-striped table-hover mb-0" data-table>
              <thead class="bg-secondary">
                <tr>
                  <th scope="col" class="text-white">N° TABLE</th>
                  <th scope="col" class="text-white">EXAMEN</th>
                  <th scope="col" class="text-white">STATUT</th>
                  <th scope="col" class="text-white">ACTIONS</th>
                </tr>
              </thead>
              <tbody>
                {% for enrollment in enrollments %}
                <tr>
                  <td>{{ enrollment.table_num }}</td>
                  <td>{{ enrollment.get_exam_display }}</td>
                  <td>
                    {% if enrollment.is_marked %}
                      <span class="badge badge-success">NOTÉ</span>
                    {% else %}
                      <span class="badge badge-danger">NON-NOTÉ</span>
                    {% endif %}
                  </td>
                  <td>
                    <a class="btn btn-sm btn-primary" 
                       hx-get="{% url 'grade_add' %}?center={{ enrollment.center.id }}&table_num={{ enrollment.table_num }}&updating_grade=1" 
                       hx-target="#main-content">
                      <span data-feather="edit"></span> Noter
                    </a>
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="4" class="text-center">Aucun élève trouvé</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
            {% include 'components/pagination.html' with include_items='[name=search], [name=center], [name=per_page]' %}
          </div>
        </div>
      </div>
    </div>
  {% else %}
    <div class="alert alert-info mt-3">
      Veuillez sélectionner un centre pour afficher la liste des élèves.
    </div>
  {% endif %}
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>
