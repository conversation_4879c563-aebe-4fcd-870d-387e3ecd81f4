{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Notes</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">LISTE DES ELEVES NON-NOTES - EXAMEN NATIONAL</h5>
  </div>
  <!-- <div class="mb-3">
    <p>Elèves Non notés: <strong>{{ mock_exam_unmarked }} / {{ candidates }}</strong></p>
  </div> -->

    <div class="row d-flex justify-content-center my-2 {% if not user.role == ROLE_COMMISSION_NATIONALE %} d-none {% endif %}">
      <div class="wrapper col-md-6">
        Localité:
        <select name="locations_select" id="locations_select" 
          class="form-control" hx-get="{% url 'students_unmarked' %}?exam={{ exam }}" 
          hx-target="#main-content">
          <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
          {% for location in locations_list %}
            <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
          {% empty %}
          {% endfor %}
        </select>
      </div>
    </div>
  
    {% include 'components/search_and_page_size.html' with include_items='[name=locations_select]' %}
  <div class="row" id="table-wrapper">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0" data-table>
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">N° TABLE</th>
                <th scope="col" class="text-white">CENTRE</th>
                <th scope="col" class="text-white">LOCALITE</th>
                <th scope="col" class="text-white">STATUT</th>
              </tr>
            </thead>
            <tbody>
              {% for enrollment in enrollments %}
              <tr>
                <td>{{ enrollment.table_num }}</td>
                <td>{{ enrollment.center }}</td>
                <td>{{ enrollment.center.location }}</td>
                <td class="text-danger">NON-NOTE</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items='[name=search], [name=locations_select]' %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>