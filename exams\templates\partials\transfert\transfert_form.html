{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">DEMANDE DE TRANSFERT</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Infos élève et origine</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.enrollment.id_for_label }}">Elève</label>
                 {% render_field form.enrollment class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.old_school.id_for_label }}">Ecole actuelle</label>
                {% render_field form.old_school class='form-control' %}
            </div>
        </div>
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold">Infos Ecole d'accueil</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-md-6">
                <label for="location">Commission Locale d'accueil</label>
                <select name="location" id="location" 
                    class="form-control" hx-get="{% url 'location_centers' %}?all_schools=1" 
                    hx-target="#school_container">
                    <option value="0">Sélectionner</option>
                    {% for location in locations %}
                    <option value="{{ location.id }}">{{ location }}</option>
                    {% endfor %}
              </select>
            </div>
            <div class="form-group mb-2 col-md-6" id="school_container">
                <label for="{{ form.school.id_for_label }}">Ecole d'accueil</label>
                {% render_field form.school class='form-control' %}
            </div>

        </div>

    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>