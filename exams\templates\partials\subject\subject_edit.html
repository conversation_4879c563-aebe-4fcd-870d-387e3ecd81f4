{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">{% if subject %} MODIFIER MATIERE {{ subject|upper }} {% else %} AJOUTER MATIERE {{ exam|upper }} {% endif %}</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Infos matière</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.exam.id_for_label }}">Examen *</label>
                 {% render_field form.exam class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.group.id_for_label }}">Catégorie *</label>
                 {% render_field form.group class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.name.id_for_label }}">Nom en français *</label>
                 {% render_field form.name class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6" id="location_centers">
                <label for="{{ form.translation.id_for_label }}">Traduction *</label>
                 {% render_field form.translation class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-3">
                <label for="{{ form.coefficient.id_for_label }}">Coefficient</label>
                {% render_field form.coefficient class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-3">
                <label for="{{ form.order.id_for_label }}">Ordre d'apparution</label>
                {% render_field form.order class='form-control' %}
            </div>
        </div>

        <div class="form-row">
            <div class="form-group mb-2 ml-3">
                <div class="custom-control custom-switch">
                    {% render_field form.active class='custom-control-input' %}
                    <label class="custom-control-label" for="{{ form.active.id_for_label }}">Actif</label>
                </div>
            </div>
        </div>

    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>