{% load static %}
{% load humanize %}

<div class="container-fluid mt-2" hx-get="{{ request.path }}" hx-target="#main-content" hx-trigger="saved from:body">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Examen Blanc National</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      EXAMENS BLANCS
    </h5>
  </div>
  <div class="container-fluid">
    {% if request.GET.show_msg == '1' %}
    <div class="alert alert-info border" id="info-alert">
      <span data-feather="info" class="align-middle feather-16 feather-info"></span>
      Notation terminée avec succès
    </div>
    {% else %}
    <div class="alert alert-success border">
      <span data-feather="info" class="align-middle feather-16 feather-success"></span>
      Seule la moyenne générale obtenu à l'examen blanc doit être renseignée
    </div>
    {% endif %}
  </div>
  <div class="d-flex justify-content-between row mb-2 {% if user.role == ROLE_ECOLE %}d-none{% endif %}">
    <div class="wrapper col-6 {% if not user.role == ROLE_COMMISSION_NATIONALE %} d-none {% endif %}">
      Localité:
      <select name="locations_select" id="locations_select" 
        class="form-control" hx-get="{% url 'mock_exam_students' %}?exam={{ exam }}" 
        hx-target="#main-content">
        <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
        {% for location in locations_list %}
          <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
        {% empty %}
        {% endfor %}
      </select>
    </div>
    
    <div class="wrapper col-6 {% if user.role == ROLE_ECOLE %} d-none {% endif %}">
      Ecole:
      <select name="schools_select" id="schools_select" 
      class="form-control"
      hx-get="{% url 'mock_exam_students' %}?exam={{ exam }}" hx-target="#main-content" hx-include="[name=locations_select]">
        <option value="0">Sélectionner</option>
        {% for school in schools_list %}
        <option value="{{ school.id }}" 
        {% if selected_school == school.id %} selected {% endif %}>{{ school }}</option>
        {% empty %}
        {% endfor %}
      </select>
    </div>
  </div>

  {% if selected_school and perms.exams.edit_mock_exam_grade and first_unmarked %}
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
    <div>
      <a class="btn btn-success text-white btn-sm"
         hx-get="{% url 'mock_exam_grade_edit' first_unmarked %}?exam=cepe&school={{ selected_school }}"
         hx-target="#main-content">
         Commencer/Continuer la saisie</a>
    </div>
  </div>
  {% endif %}
  <div class="container d-flex mb-3 justify-content-around">
    <span class="badge badge-success p-2">Elèves Notés: {{ stats.marked }} / {{ students_count }}</span>
  </div>

  {% include 'components/search_and_page_size.html' with include_items='[name=schools_select], [name=locations_select], [name=search], [name=per_page]'%}
  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">MATRICULE</th>
                <th scope="col" class="text-white">NOM ET PRENOMS</th>
                <th scope="col" class="text-white">MOY.</th>
                <th scope="col" class="text-white">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for enrollment in enrollments %}
              <tr>
                <td>{% if enrollment.student.student_id %} {{ enrollment.student.student_id }} {% else %} {{ enrollment.student.identifier }} {% endif %}</td>
                <td>{{ enrollment }}</td>
                <td>{{ enrollment.mock_average|default:'NC' }}</td>
                <td>
                    {% if perms.exams.edit_mock_exam_grade %}
                    <a href="" class="dropdown-item btn btn-sm"
                    hx-get="{% url 'mock_exam_grade_edit' enrollment.pk %}?school={{ enrollment.school_id }}&exam={{ exam }}&one_only=1"
                    hx-target="#main-content"> <span data-feather="edit"></span></a>
                    {% endif %}
                </td>
                
              </tr>
              {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items='[name=search], [name=per_page], [name=schools_select], [name=locations_select]' %}
        </div>
      </div>
    </div>
  </div>
</div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>