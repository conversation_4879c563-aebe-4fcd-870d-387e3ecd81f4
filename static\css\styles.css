:root {
	--bg-light-green: #C6EBC9;
}

.img-thumbnail {
	width: 55px;
	height: 55px;
	object-fit: contain;
}

.bg-light-green {
	background-color: var(--bg-light-green);
}

.fw-bold {
	font-weight: 400;
}

/* .pace-running #main-content, 
.pace-running .adminx-sidebar {
    display: none;
} */

/* Bottom navbar
 */
/* Bottom nav */
#bottom-nav .nav-link {
	color: rgb(59, 58, 58);
}
#bottom-nav .nav-item {
	font-size: smaller !important;

}
#bottom-nav .nav-item .icon {
	width: 18px;
	height: 18px;
}
#bottom-nav .nav-link.active,  
#bottom-nav .nav-link:hover{
	color: rgb(0, 0, 0);
	background-color: rgb(205, 227, 235);
}

@media (min-width: 768px) {
	#bottom-nav {
	  display: none !important;
	}
  }

/* Pace.js  */
.pace {
	-webkit-pointer-events: none;
	pointer-events: none;

	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;

	overflow: hidden;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 2000;
	width: 100%;
	height: 12px;
	background: #fff;
}

.pace-inactive {
	display: none;
}

.pace .pace-progress {
	background-color: #29d;
	position: fixed;
	top: 0;
	bottom: 0;
	right: 100%;
	width: 100%;
	overflow: hidden;
	height: 12px;
}

.pace .pace-activity {
	position: fixed;
	top: 0;
	right: -32px;
	bottom: 0;
	left: 0;
	height: 12px;

	-webkit-transform: translate3d(0, 0, 0);
	-moz-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0);
	-o-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);

	background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.2)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.2)), color-stop(0.75, rgba(255, 255, 255, 0.2)), color-stop(0.75, transparent), to(transparent));
	background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
	background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
	background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
	-webkit-background-size: 32px 32px;
	-moz-background-size: 32px 32px;
	-o-background-size: 32px 32px;
	background-size: 32px 32px;

	-webkit-animation: pace-theme-barber-shop-motion 500ms linear infinite;
	-moz-animation: pace-theme-barber-shop-motion 500ms linear infinite;
	-ms-animation: pace-theme-barber-shop-motion 500ms linear infinite;
	-o-animation: pace-theme-barber-shop-motion 500ms linear infinite;
	animation: pace-theme-barber-shop-motion 500ms linear infinite;
}

@-webkit-keyframes pace-theme-barber-shop-motion {
	0% { -webkit-transform: none; transform: none; }
	100% { -webkit-transform: translate(-32px, 0); transform: translate(-32px, 0); }
}
@-moz-keyframes pace-theme-barber-shop-motion {
	0% { -moz-transform: none; transform: none; }
	100% { -moz-transform: translate(-32px, 0); transform: translate(-32px, 0); }
}
@-o-keyframes pace-theme-barber-shop-motion {
	0% { -o-transform: none; transform: none; }
	100% { -o-transform: translate(-32px, 0); transform: translate(-32px, 0); }
}
@-ms-keyframes pace-theme-barber-shop-motion {
	0% { -ms-transform: none; transform: none; }
	100% { -ms-transform: translate(-32px, 0); transform: translate(-32px, 0); }
}
@keyframes pace-theme-barber-shop-motion {
	0% { transform: none; transform: none; }
	100% { transform: translate(-32px, 0); transform: translate(-32px, 0); }
}

select {
	height: 36px !important;
}

@keyframes blink {
	0%,
	50%,
	100% {
		opacity: 1;
	}

	25%,
	75% {
		opacity: 0;
	}
}

#info-alert {
	animation: blink 2s ease 0s 2 normal forwards;
}

.sidebar-nav-submenu {
	padding-left: 1.5rem;
	list-style: none;
	transition: all 0.3s ease-in;
  }
  
  .sidebar-nav-submenu .sidebar-nav-link {
	padding-left: 1rem;
  }
  
  .sidebar-nav-submenu .sidebar-nav-icon {
	font-size: 0.875rem;
  }
  
  /* Animation for chevron */
  [data-feather="chevron-down"] {
	transition: transform 0.3s ease-in;
  }

  /* Main sidebar container */
  .adminx-sidebar {
	background: linear-gradient(135deg, #37474F 0%, #546E7A 100%);
	border-right: 1px solid rgba(0, 0, 0, 0.1);
	box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  }

  /* Reset background for nav links and sub-nav */
  .sidebar-nav-link, .sidebar-sub-nav {
	background: transparent !important;
	color: rgba(255, 255, 255, 0.9) !important;
	transition: all 0.3s ease;
	border: none;
	position: relative;  /* For active indicator */
	padding: 0.5rem 1rem;
	border-radius: 4px;
	margin: 0.125rem 0.5rem;
  }

  /* Hover state */
  .sidebar-nav-link:hover {
	color: #ffffff !important;
	background: rgba(255, 255, 255, 0.12) !important;
	transform: translateX(5px);
  }

  /* Active state with multiple visual indicators */
  .sidebar-nav-link.active,
  .sidebar-nav-item.has-active-submenu > .sidebar-nav-link {
	color: #ffffff !important;
	background: rgba(255, 255, 255, 0.18) !important;
	font-weight: 600;
	transform: translateX(5px);
  }

  /* Left border indicator for active state */
  .sidebar-nav-link.active::before,
  .sidebar-nav-item.has-active-submenu > .sidebar-nav-link::before {
	content: '';
	position: absolute;
	left: -0.5rem;
	top: 0;
	height: 100%;
	width: 3px;
	background: #ffffff;
	border-radius: 0 4px 4px 0;
  }

  /* Icon animation for active state */
  .sidebar-nav-link.active .sidebar-nav-icon,
  .sidebar-nav-item.has-active-submenu > .sidebar-nav-link .sidebar-nav-icon {
	opacity: 1;
	transform: scale(1.1);
  }

  /* Submenu styling - slightly different active state */
  .sidebar-nav-submenu .sidebar-nav-link.active {
	background: rgba(255, 255, 255, 0.15) !important;
	border-radius: 0 4px 4px 0;
	/* No transform for submenu items to maintain alignment */
	transform: none;
  }

  /* Enhance sidebar icons */
  .sidebar-nav-icon {
	opacity: 0.9;
	margin-right: 0.5rem;
	transition: all 0.3s ease;
  }

  /* Submenu styling */
  .sidebar-nav-submenu {
	background: transparent;
	border: none;
	margin-left: 0.75rem;
	padding-left: 0.25rem;
	border-left: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Remove default borders */
  .sidebar-nav-item {
	border: none;
  }

  /* Optional subtle separators */
  .sidebar-nav > .sidebar-nav-item:not(:last-child) {
	border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .sidebar-nav-link {
	position: relative;
	overflow: hidden;
	cursor: pointer;
	user-select: none;
  }
  
  .ripple {
	position: absolute;
	border-radius: 50%;
	transform: scale(0);
	animation: ripple 0.6s linear;
	background-color: rgba(255, 255, 255, 0.4);
	pointer-events: none;
  }
  
  @keyframes ripple {
	to {
	  transform: scale(4);
	  opacity: 0;
	}
  }
  
  /* Improve hover state */
  .sidebar-nav-link:hover {
	transform: translateY(-1px);
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }
  
  /* Active state enhancement */
  .sidebar-nav-link.active {
	box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
	transform: translateY(0);
  }
  
  /* Smooth transitions */
  .sidebar-nav-link {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Optional: Add a subtle hover effect for icons */
  .sidebar-nav-icon {
	transition: transform 0.2s ease;
  }
  
  .sidebar-nav-link:hover .sidebar-nav-icon {
	transform: scale(1.1);
  }

  .waves-ripple {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: auto !important;
	height: auto !important;
  }
  
  li.waves-effect {
	display: list-item !important;
  }
  
  .waves-ripple {
	background-color: rgba(255, 255, 255, 0.3) !important; /* Light ripple for gray backgrounds */
  }
  
  .nav-item.waves-effect .waves-ripple {
	background-color: rgba(144, 238, 144, 0.7) !important; /* Light ripple for gray backgrounds */
  }
  
  .feather-16 {
	width: 16px;
	height: 16px;
  }

  .material-form .form-group {
	position: relative;
  }

  /* Override Bootstrap's form-control */
  .material-form .form-control {
	width: 100% !important;
	padding: 0.2rem 0.4rem  !important;
	padding-left: .75rem !important;
	font-size: 1rem !important;
	border: 2px solid #e0e0e0 !important;
	border-radius: 4px !important;
	outline: none !important;
	transition: all 0.2s ease-in-out !important;
	height: auto !important;
	background: transparent !important;
  }

  .material-form .form-label {
	position: absolute !important;
	left: 0.75rem !important;
	top: 0.75rem !important;
	padding: 0 0.25rem !important;
	font-size: 1rem !important;
	color: #bbbbbb !important;
	cursor: text !important;
	transition: all 0.2s ease-in-out !important;
	background: white !important;
	pointer-events: none !important;
	margin: 0 !important;
	line-height: 1 !important;
  }

  .material-form .form-control:focus {
	border-color: #059820 !important;
	box-shadow: none !important;
  }

  .material-form .form-control:focus + .form-label,
  .material-form .form-control:not(:placeholder-shown) + .form-label {
	top: -0.5rem !important;
	left: 0.5rem !important;
	font-size: 0.875rem !important;
	color: #2563eb !important;
	background: white !important;
	padding: 0 0.25rem !important;
  }

  .material-form .form-control::placeholder {
	color: transparent !important;
  }

  /* .material-form .btn-primary {
	width: 100%;
	padding: 0.75rem;
	background: #2563eb !important;
	color: white;
	border: none !important;
	border-radius: 4px;
	font-size: 1rem;
	font-weight: 500;
	cursor: pointer;
	transition: background-color 0.2s ease-in-out;
  }

  .material-form .btn-primary:hover {
	background: #1d4ed8 !important;
  } */

  .material-form textarea.form-control {
	min-height: 100px !important;
	resize: vertical !important;
  }

  /* Error states */
  .material-form .form-control.is-invalid {
	border-color: #dc2626 !important;
  }

  .material-form .form-control.is-invalid + .form-label {
	color: #dc2626 !important;
  }

  .material-form .invalid-feedback {
	color: #dc2626 !important;
	font-size: 0.875rem !important;
	margin-top: 0.25rem !important;
  }

    /* Dashboard Layout */
	.adminx-main-content {
		background: #f8f9fa;
		min-height: 100vh;
		padding: 1.5rem 0;
	}
	
	/* Card Styles */
	.card {
		border: none;
		margin-bottom: 1.5rem;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		border-radius: 12px;
	}


	.card-hover:hover {
		transform: translateY(-4px);
		box-shadow: 0 12px 20px rgba(0,0,0,0.15);
	}
	
	.card-icon {
		min-width: 80px;
		font-weight: 600;
		font-size: 0.9rem;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* Gradient Backgrounds */
	.alert-primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}
	
	.alert-warning {
	  /* Use a blue color gradient */
		background: linear-gradient(135deg, #3340f8 0%, #68c1fc 100%);
		color: #ffffff;
	}
	
	.alert-success {
		background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
		color: #2d3748;
	}
	
	.bg-success {
		background: linear-gradient(135deg, #0a8a05 0%, #19f562 100%) !important;
	}
	
	.bg-info {
		background: linear-gradient(135deg, #fab622 0%, #fd7215 100%) !important;
	}
	
	/* Typography */
	h4 {
		font-size: 1.25rem;
		font-weight: 600;
		margin-bottom: 1.5rem;
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}
	
	h4 .feather {
		stroke-width: 2.5;
	}
	
	.card-body h5 {
		font-size: 0.9rem;
		letter-spacing: 0.5px;
	}
	
	.card-body h5.card-title {
		font-size: 1.5rem;
		font-weight: 700;
	}
	
	/* Stats Cards */
	.card-body {
		padding: 1.25rem;
	}
	
	.card .border {
		border: none !important;
		background: rgba(255, 255, 255, 0.1);
	}
	
	/* Breadcrumb */
	.breadcrumb {
		background: transparent;
		padding: 0;
		margin-bottom: 2rem;
	}
	
	.breadcrumb-item a {
		color: #6b7280;
		text-decoration: none;
	}
	
	.breadcrumb-item.active {
		color: #374151;
	}
	
	/* Alert */
	.alert {
		border-radius: 12px;
		border: none;
		box-shadow: 0 2px 4px rgba(0,0,0,0.05);
	}
	
	/* Responsive */
	@media (max-width: 768px) {
		.card-icon {
			min-width: 60px;
		}
		
		.card-body h5 {
			font-size: 0.85rem;
		}
		
		.card-body h5.card-title {
			font-size: 1.25rem;
		}
	}
	
	/* Animation */
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.row > div {
		animation: fadeIn 0.6s ease-out forwards;
	}
	
	.row > div:nth-child(2) {
		animation-delay: 0.1s;
	}
	
	.row > div:nth-child(3) {
		animation-delay: 0.2s;
	}
	.page-login .btn {
		height: 48px;
		font-weight: 500;
		letter-spacing: 0.5px;
		text-transform: uppercase;
		transition: all 0.3s ease;
		border-radius: 8px;
		border: none;
		color: white;
		box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
	}
	
	.page-login .btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
	}
	
	.page-login .btn:active {
		transform: translateY(1px);
	}
	
	.table {
		font-size: small !important;
	}
