<div class="form-group row material-form">
    <div class="col-8">
      <label for="search" class="pl-2">Rechercher:</label>
      <input type="search" name="search" id="search" 
              class="form-control btn-sm ml-2"
              value="{{ search }}"
              hx-get="{{ request.path }}" hx-target="#main-content"
              {% if include_items %} hx-include="{{ include_items }}" {% endif %}>
    </div>
    <div class="col-4">
      <label for="search" class="pl-2">Afficher</label>
      <select name="per_page" id="per_page" class="form-control form-control-sm" 
          hx-get="{{ request.path }}?page={{ page }}&search={{ search }}"
          hx-target="#main-content"
          hx-include="{% if include_items %} {{ include_items }} {% else %} [name=search], [name=per_page] {% endif %}">
        <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 élément(s)</option>
        <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 élément(s)</option>
        <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 élément(s)</option>
        <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 élément(s)</option>
      </select> 
    </div>
</div>

{% if result_found %}
  <div class="alert alert-warning font-weight-bold d-flex justify-content-between border">
    <span><span data-feather="filter" class="feather-16 align-middle mr-2"></span> {{ result_found }} résultats</span>
  </div>
{% endif %}