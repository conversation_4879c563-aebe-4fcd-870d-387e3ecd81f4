import sys
from io import BytesIO
from PIL import Image, ImageOps
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.core.cache import cache
from django.db.models import Count, Q, Sum
from . import constants
from exams import models as exams_models


def get_current_year():
    cache_key = 'current_year'
    cached_object = cache.get(cache_key)
    if cached_object:
        return cached_object
    result = exams_models.Year.objects.filter(is_current=True)

    # Cache for one day
    # Cache will update when current Year object is changed
    cache.set(cache_key, result.first(), timeout=60 * 60 * 5)
    return result.first()


def get_selected_year(request):
    """
    Get the selected year from session, fallback to current year if not found.

    Args:
        request: Django request object with session

    Returns:
        Year object: The selected year from session or current year as fallback
    """
    selected_year_id = request.session.get('selected_school_year_id')

    if selected_year_id:
        try:
            return exams_models.Year.objects.get(id=selected_year_id)
        except exams_models.Year.DoesNotExist:
            # If the year in session doesn't exist anymore, fall back to current year
            pass

    # Fallback to current year
    return get_current_year()

def get_exam_fees(year, exam, user):
        candidates_count = 0
        candidates_count = exams_models.Enrollment.candidates\
            .get_candidates(
             user=user, year=year, exam=exam, confirmed=False
        ).count()        
        return compute_exam_fees(candidates_count, year, exam)

def compute_exam_fees(candidates_count, year, exam):
     return candidates_count * year.get_price_for(exam)

def get_statistics_into_context(year, user):
    context = {}
    if year and user:
        students_count_qs = exams_models.Enrollment\
            .candidates.get_candidates(year=year, user=user, confirmed=False) \
            .aggregate(
             cepe_students_count=Count('id', filter=Q(exam=constants.EXAM_CEPE)),
             bepc_students_count=Count('id', filter=Q(exam=constants.EXAM_BEPC)),
             bac_students_count=Count('id', filter=Q(exam=constants.EXAM_BAC)),
             
             cepe_candidates=Count('id', filter=Q(exam=constants.EXAM_CEPE) & Q(confirmed=True)),
             bepc_candidates=Count('id', filter=Q(exam=constants.EXAM_BEPC) & Q(confirmed=True)),
             bac_candidates=Count('id', filter=Q(exam=constants.EXAM_BAC) & Q(confirmed=True)),
        )

        # cards_qs = exams_models.StudentCard.objects.filter(year=year)
        
        context['cepe_students_count'] = students_count_qs['cepe_students_count']
        context['bepc_students_count'] = students_count_qs['bepc_students_count']
        context['bac_students_count'] = students_count_qs['bac_students_count']

        context['cepe_candidates'] = students_count_qs['cepe_candidates']
        context['bepc_candidates'] = students_count_qs['bepc_candidates']
        context['bac_candidates'] = students_count_qs['bac_candidates']

        context['cepe_candidates_fees'] = compute_exam_fees(
            context['cepe_candidates'], year, exam=constants.EXAM_CEPE
        )
        context['bepc_candidates_fees'] = compute_exam_fees(
            context['bepc_candidates'], year, exam=constants.EXAM_BEPC
        )
        context['bac_candidates_fees'] = compute_exam_fees(
            context['bac_candidates'], year, exam=constants.EXAM_BAC
        )    
    return context

def generate_identifier(student_id, exam:str):
    """ Generates and returns a zero-padded student identifier
        preceded by last 4 digits of current year """
    year = get_current_year().short_name
    student_id = str(student_id)
    zero_padded_digits = student_id.rjust(
        constants.IDENTIFIER_DIGITS_COUNT, '0')
    return f"{exam.upper()}{year}{zero_padded_digits}"

def generate_school_identifier(school_id):
         """ Generates and returns a zero-padded a school identifier"""
         return f"{str(school_id).rjust(3, '0')}"

def compress_image(img, width=None, height=None):
    output_img = Image.open(img)
    img_format = output_img.format
    bytes_io = BytesIO()

    output_img.thumbnail([
        width or constants.COMPRESSED_IMAGE_WIDTH, 
        height or constants.COMPRESSED_IMAGE_HEIGHT])
    
    if img_format == "JPEG":
        output_img = output_img.convert("RGB")

    output_img = ImageOps.exif_transpose(output_img)
    output_img.save(bytes_io, format=img_format, optimize=True)
    bytes_io.seek(0)
    return InMemoryUploadedFile(bytes_io, 'ImageField', 
        img.name,  'image/jpeg', sys.getsizeof(bytes_io), None)


class CachedCreateViewMixin:
    cache_timeout = 60 * 60 * 24 * 2  # cache for 2 days

    def dispatch(self, request, *args, **kwargs):
        key = self.get_cache_key(request)
        cached = cache.get(key)
        if cached is not None:
            return cached
        response = super().dispatch(request, *args, **kwargs)
        response.render()
        cache.set(key, response, self.cache_timeout)
        return response

    def get_cache_key(self, request):
        return request.get_full_path()

def get_cache_for_user(obj_name, user, year=None):
    extra = ''
    if year:
        extra = f'_{year}'

    cache_key = f'{obj_name}_for{extra}'
    if user.role == constants.ROLE_COMMISSION_LOCALE:
        cache_key = f'{obj_name}_for_{user}_{year}'
    return cache_key.strip()

def clean_exam_selected(request):
    exam = request.GET.get('exam')
    return exam_or_default(exam)

def exam_or_default(exam :str):
    return exam.lower() if exam_is_valid(exam) else constants.EXAM_CEPE

def exam_is_valid(exam):
    return exam and exam.lower() in constants.EXAMS_LIST

def get_exam_translation(exam):
    if exam.lower() == constants.EXAM_CEPE:
        return constants.CEPE_DEGREE_TRANSLATION
    elif exam.lower() == constants.EXAM_BEPC: 
        return constants.BEPC_DEGREE_TRANSLATION
    return ''

def get_decision(year, exam, average, gender, french=True):
        min_avg = year.get_min_avg(exam) or 10
        decisions = constants.DECISIONS_FRENCH
        if not french:
            decisions = constants.DECISIONS_TRANSLATION
        
        key = 'male_' if gender == constants.GENDER_MALE else 'female_'
        key += 'admitted' if average >= min_avg else 'declined'
        return decisions.get(key, ' ')


def confirm_center(center):
    """ Confirms the center and assigns candidates to rooms """
    total_candidates = 0
    total_rooms_capacity = 0
    schools_without_center = exams_models.School.objects \
        .annotate(students=Count('enrollment', 
                                 filter=Q(enrollment__confirmed=True) & \
                                        Q(enrollment__year=center.year) & \
                                        Q(enrollment__exam=center.exam)), 
                center_schools_for_year=Count(
                            'school_centers', filter=Q(school_centers__year=center.year) & Q(school_centers__exam=center.exam)
                        )) \
        .filter(location=center.location, center_schools_for_year=0, students__gte=1)
    
    if schools_without_center.exists():
        center.comment = "Des écoles de la commission n'ont pas de centre"
        center.complete = False
        center.save(update_fields=['comment', 'complete'])
        cache.delete('centers')
        return

    if center.center_schools.exists():
        rooms = center.room_set.annotate(count=Count('enrollment', distinct=True))
        total_rooms_capacity = exams_models.Room.objects.filter(center=center) \
            .values_list('capacity', flat=True) \
            .aggregate(total_capacity=Sum('capacity'))['total_capacity'] or 0
        
        total_candidates = exams_models.Enrollment.objects.filter(
                school__in=center.center_schools.all(),
                confirmed=True,
                year=center.year,
                exam=center.exam
            ).count() or 0
        
        print('Center: ', str(center), 'Room capacity', total_rooms_capacity, 'Total candidates', total_candidates)

        if (total_candidates and total_candidates > 0) and (total_rooms_capacity and total_candidates <= total_rooms_capacity):
            for school in center.center_schools.all():
                candidates = school.enrollment_set \
                    .filter(confirmed=True) \
                    .order_by('student__last_name', 'student__first_name')
                candidates = candidates.filter(year=center.year, exam=center.exam)
                candidates.update(center=center)


            for room in rooms:
                center_id = center.id
                candidates = center.enrollment_set \
                    .filter(room__isnull=True, confirmed=True) \
                    .order_by('student__last_name', 'student__first_name')
                
                if candidates.exists():
                    capacity = room.capacity
                    remaining = capacity - room.count
                    
                    # Put candidates in room
                    num = room.count + 1
                    for candidate in candidates[:remaining]:
                        candidate.room = room 

                        if not candidate.table_num:
                            candidate.table_num = f'{center_id}'.zfill(3) + f'{room.number}'.zfill(2) + f'{num}'.zfill(2)  
                        
                        if not candidate.anonymat:
                            candidate.anonymat = f'{center_id}'.zfill(3) + f'{candidate.id}'.zfill(4)  
                        
                        candidate.save(update_fields=['room', 'anonymat', 'table_num'])
                        num = num + 1
            center.complete = True
            center.comment = 'OK'
            cache.delete('rooms')
        elif total_candidates == 0:
            pass
        else:
            center.complete = False
            center.comment = 'Nbre de candidats sup. à capacité des salles'

        center.save(update_fields=['complete', 'comment'])
        cache.delete('centers')


def update_annual_average(enrollment):
    """ Computes and updates the annual average """
    mock_avg = enrollment.mock_average
    exam_avg = enrollment.average
    
    if not mock_avg:
        enrollment.gen_average = exam_avg
        return enrollment 
    
    if exam_avg == None:
        exam_avg = 0

    enrollment.gen_average = round(((mock_avg * 10) + (exam_avg * 90)) / 100, 2)
    print(f'Actualisation des moyennes de {enrollment}')
    return enrollment