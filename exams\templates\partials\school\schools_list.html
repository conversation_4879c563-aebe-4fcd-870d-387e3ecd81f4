{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Ecoles</li>
    </ol>
  </nav>
  {% if enrollment_closed %}
  <div class="mx-2">
    <div class="alert alert-warning" role="alert">
      <span data-feather="alert-triangle"></span>
      Les enregistrements des écoles sont fermés
    </div>
  </div>
  {% endif %}
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      LISTE DES ECOLES
      {% if user.role == ROLE_COMMISSION_LOCALE %} DE {{ user.localcommission }} {% endif %}   
      ({{ schools_count }})
    </h5>
  </div>

  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
      {% if perms.exams.add_school %}
      <div>
        <a class="btn btn-success text-white"
           hx-get="{% url 'school_add' %}"
           hx-target="#dialog">
           + Ajouter une école</a>
      </div>
      {% endif %}

      {% if perms.exams.view_school %}
      <div class="dropdown">
        <button class="btn btn-warning dropdown" type="button" 
                data-toggle="dropdown" aria-expanded="false">
          <i data-feather="file-text"></i> Imprimer
        </button>
        <div class="dropdown-menu">
            <a class="dropdown-item" 
              href="{% url 'schools_pdf' %}">Liste des écoles</a>
        </div>
      </div>
      {% endif %}
  </div>

  <div class="form-group row">
    <div class="col-8">
      <label for="search" class="pl-2">Rechercher:</label>
      <input type="search" name="search" id="search" 
              class="form-control btn-sm ml-2"
              value="{{ search }}"
              hx-get="{{ request.path }}" 
              hx-target="#main-content"
              hx-include="[name=per_page]">
    </div>
    <div class="col-4">
      <label for="per_page" class="pl-2">Afficher</label>
      <select name="per_page" id="per_page" class="form-control form-control-sm" hx-get="{{ request.path }}?page={{ page }}"
              hx-target="#main-content" hx-include="[name=search]">
        <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 éléments</option>
        <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 éléments</option>
        <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 éléments</option>
        <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 éléments</option>
      </select> 
    </div>
  </div>

  {% if result_found %}
  <div class="alert alert-warning font-weight-bold d-flex justify-content-between border">
    <span><span data-feather="filter" class="feather-16 align-middle mr-2"></span> {{ result_found }} résultat(s)</span>
  </div>
  {% endif %}

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0" data-table>
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">CODE</th>
                <th scope="col" class="text-white">ECOLE</th>
                <th scope="col" class="text-white">TRADUCTION</th>
                <th scope="col" class="text-white">STATUT</th>
                <th scope="col" class="text-white">DIRECTEUR</th>
                <th scope="col" class="text-white">CONTACT</th>

                {% if user.role != ROLE_ECOLE %}
                  <th scope="col" class="text-white">LOCALITE</th>
                  {% endif %}
                <th scope="col" class="text-white">DRENA</th>
                <th scope="col" class="text-white">IEPP</th>
                <!-- <th scope="col" class="text-white">CEPE</th>
                <th scope="col" class="text-white">BEPC</th>
                <th scope="col" class="text-white">BAC</th> -->
                {% if perms.exams.view_school %}
                <th scope="col" class="text-white">Mod.</th>
                <th scope="col" class="text-white">Valider</th>
                <th scope="col" class="text-white">Impr.</th>
                {% endif %}
              </tr>
            </thead>
            <tbody>
              {% for school in schools %}
              <tr>
                <td>{{ school.identifier }}</td>
                <td>{{ school.name|upper }}</td>
                <td>{{ school.name_ar|default:'-' }}</td>
                <td>
                  {% if school.confirmed %} 
                  <span class="badge badge-pill badge-success">RECONNU</span>
                  {% else %}  
                    <span class="badge badge-pill badge-warning">NON RECONNU</span>
                  {% endif %}
                </td>
                <td>{{ school.director|upper }}</td>
                <td>{{ school.phone }}</td>
                {% if user.role != ROLE_ECOLE %}
                  <td>{{ school.local_commission }}</td>
                {% endif %}
                <td>{{ school.drena_obj|default_if_none:'-' }}</td>
                <td>{{ school.iepp|default_if_none:'-' }}</td>
                <!-- <td>{{ school.cepe_students_count }}</td> -->
                <!-- <td>{{ school.bepc_students_count }}</td>
                <td>{{ school.bac_students_count }}</td> -->
                <td>
                  {% if perms.exams.change_school %}
                    <a href="" class="dropdown-item btn btn-sm btn-warning" hx-get="{% url 'school_edit' school.id %}"
                    hx-target="#dialog"> <span data-feather="edit"></span></a>
                  {% endif %}
                </td>
                <td>
                  {% if perms.exams.change_school_status %}
                  <a class="dropdown-item btn btn-sm btn-info" href="#"
                    hx-get="{% url 'school_confirm' school.id %}"
                    hx-target="#dialog"
                  ><span data-feather="check-square"></span></a>
                  {% endif %}
                </td>
                <td class="d-flex w-auto">
                  <button class="btn btn-primary btn-sm dropdown" type="button" data-toggle="dropdown" aria-expanded="false">
                    <i data-feather="more-horizontal"></i>
                    </button>
                    <div class="dropdown-menu">
                      <a class="dropdown-item btn btn-sm" href="{% url 'candidates_pdf' 'cepe' %}?school_id={{ school.id }}">
                        <span data-feather="file-text"></span>Candidats CEPE</a>
                      <a class="dropdown-item btn btn-sm" href="{% url 'candidates_pdf' 'bepc' %}?school_id={{ school.id }}">
                        <span data-feather="file-text"></span>Candidats BEPC
                      </a>
                    <a href="{% url 'candidates_pdf' 'bac' %}?school_id={{ school.id }}" class="dropdown-item btn btn-sm">
                      <span data-feather="file-text"></span>Candidats BAC</a>
                    </div>
                </td>
                
              </tr>
              {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items="[name=per_page], [name=search]" %}
          
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>