{% load static %}
{% load humanize %}
{% load widget_tweaks %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Examen Blanc</li>
      <li class="breadcrumb-item active" aria-current="page">Importation</li>
      <li class="breadcrumb-item active" aria-current="page">Aperçu</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">VÉRIFICATION DES MOYENNES D'EXAMEN BLANC À IMPORTER</h5>
  </div>

  <div class="card mb-grid">
    <div class="card-header d-flex justify-content-between align-items-center">
      <div class="card-header-title">
        École: <strong>{{ school.name }}</strong> | 
        Fichier: <strong>{{ file_name }}</strong>
      </div>
    </div>
    <div class="card-body">
      {% if preview_data %}
        <form action="{{ action }}" method="post">
          {% csrf_token %}
          
          <div class="alert alert-info">
            <span data-feather="info" class="mr-2"></span>
            Veuillez vérifier les données avant de confirmer l'importation. 
            <strong>{{ preview_data|length }}</strong> élèves seront mis à jour.
          </div>
          
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead class="thead-light">
                <tr>
                  <th>N° Table</th>
                  <th>Nom et Prénoms</th>
                  <th>Moyenne Actuelle</th>
                  <th>Nouvelle Moyenne</th>
                  <th class="d-none">Changement</th>
                </tr>
              </thead>
              <tbody>
                {% for item in preview_data %}
                  <tr>
                    <td>{{ item.table_num }}</td>
                    <td>{{ item.name }}</td>
                    <td>{{ item.current_average }}</td>
                    <td>
                      <input type="hidden" name="enrollment_id" value="{{ item.enrollment_id }}">
                      <input type="number" name="new_average" value="{{ item.new_average }}" 
                             class="form-control form-control-sm" min="0" max="20" step="0.01">
                    </td>
                    <td class="d-none">
                      {% with diff=item.new_average|add:"-"|add:item.current_average|floatformat:2 %}
                        {% if diff > 0 %}
                          <span class="badge badge-success">+{{ diff }}</span>
                        {% elif diff < 0 %}
                          <span class="badge badge-danger">{{ diff }}</span>
                        {% else %}
                          <span class="badge badge-secondary">0</span>
                        {% endif %}
                      {% endwith %}
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          
          <div class="mt-3">
            <button type="submit" class="btn btn-success">
              <span data-feather="check-circle"></span>
              Confirmer l'importation
            </button>
            <a href="{% url 'mock_exam_import' %}?exam={{ exam }}" class="btn btn-outline-secondary ml-2">
              <span data-feather="x-circle"></span>
              Annuler
            </a>
          </div>
        </form>
      {% else %}
        <div class="alert alert-warning">
          <span data-feather="alert-triangle" class="mr-2"></span>
          Aucune donnée valide n'a été trouvée dans le fichier importé ou aucun élève correspondant n'a été trouvé.
        </div>
        <a href="{% url 'mock_exam_import' %}?exam={{ exam }}" class="btn btn-primary">
          <span data-feather="arrow-left"></span>
          Retour à l'importation
        </a>
      {% endif %}
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>
