{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item"><a href="#">Accueil</a></li>
      <li class="breadcrumb-item active" aria-current="page">Corrections {{ active_year }}</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">Liste des demandes de correction {{ exam|upper }} {{ active_year }} 
      {% if school %} DE L'ECOLE {{ school|upper }}
      {% elif location %}
      DE {{ location }}
      {% endif %}
    </h5>
  </div>

  <div class="d-flex flex-row justify-content-between mb-3">
    {% if perms.exams.add_studentcorrection %}
        <a class="btn btn-success text-white"
           hx-get="{% url 'correction_add' %}?exam={{exam}}"
           hx-target="#dialog">
           <i data-feather="plus"></i>
           Ajouter</a>
    {% endif %}
    </div>

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm d-table-sm table-actions table-striped table-bordered table-hover mb-0 datatable" data-table id="datatable" hx-boost="true">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white text-center">Photo</th>
                <th scope="col" class="text-white">Identifiant</th>
                <th scope="col" class="text-white">N° Table</th>
                <th scope="col" class="text-white">Nom et Prénoms</th>
                <th scope="col" class="text-white">Né(e) le</th>
                {% if user.role != ROLE_ECOLE %}
                <th scope="col" class="text-white">Ecole</th>
                {% endif %}
                <th scope="col" class="text-white">Approuvé?</th>
                <th scope="col" class="text-white">Approuver</th>
              </tr>
            </thead>
            <tbody id="tbody">
                {% for correction in corrections %}
                <tr>
                  <td class="align-middle text-center" style="min-width: 60px;">
                    {% if correction.student.photo %}
                      <img data-original="{{ correction.student.photo.url }}" 
                          alt="1" 
                          class="lazy border img-thumbnail rounded-circle">
                    {% endif %}
                  </td>
                  <td class="align-middle"><a>{{ correction.identifier }} </a></td>
                  <td class="align-middle">{{ correction.table_num }}</td>
                  <td class="align-middle">{{ correction.student }}</td>
                  <td class="align-middle">{{ correction.student.birth_date|date:'d/m/Y'|default_if_none:'' }} 
                  </td>
                  
                  {% if user.role != ROLE_ECOLE %}
                  <td class="align-middle">{{ correction.school|upper }}</td>
                  {% endif %}

                  <td class="align-middle text-center"><span class="badge {% if correction.status %} badge-success {% else %} badge-warning {% endif %}">
                    {% if correction.status %} OUI {% else %} NON {% endif %}
                  </span></td>
                  
                  <td class="align-middle text-center">
                    {% if not correction.status and perms.exams.change_correction_status %}
                    <div class="dropdown">
                        <button class="btn btn-secondary btn-sm" 
                                hx-get="{% url 'correction_confirm' correction.id %}" 
                                hx-target="#dialog"><i data-feather="check-square"></i></button>
                    </div>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace();
  }

</script>
