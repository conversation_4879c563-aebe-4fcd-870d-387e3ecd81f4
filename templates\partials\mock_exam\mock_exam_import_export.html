{% load static %}
{% load humanize %}
{% load widget_tweaks %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}"
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Examen Blanc</li>
      <li class="breadcrumb-item active" aria-current="page">Import/Export</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">GESTION DES MOYENNES D'EXAMEN BLANC</h5>
  </div>

  <div class="row">
    <!-- Export Card -->
    <div class="col-md-6">
      <div class="card mb-grid h-100">
        <div class="card-header bg-light text-dark">
          <div class="card-header-title">
            <i data-feather="download" class="mr-2"></i>
            Exporter les moyennes
          </div>
        </div>
        <div class="card-body">
          <p class="text-muted">Téléchargez les moyennes d'examen blanc des élèves d'une école au format Excel.</p>
          <form action="{% url 'mock_exam_excel' %}?exam={{ selected_exam }}" method="post">
            {% csrf_token %}

            {% if user.role == ROLE_COMMISSION_NATIONALE and locations_list %}
            <div class="form-group">
              <label for="school-export">Localité</label>
                <select name="locations_select" id="locations_select"
                  class="form-control" hx-get="{{ request.path }}?exam={{ selected_exam }}"
                  hx-target="#main-content">
                  <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Toutes les localités</option>
                  {% for location in locations_list %}
                    <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
                  {% empty %}
                  {% endfor %}
                </select>
            </div>
            {% endif %}
            <div class="form-group">
              <label for="school-export">École</label>
              <select name="school" id="school-export" class="form-control" required>
                <option value="">Sélectionner une école</option>
                {% for school in schools %}
                  <option value="{{ school.id }}">{{ school.name }}</option>
                {% endfor %}
              </select>
            </div>

            <button type="submit" class="btn btn-primary btn-block">
              <span data-feather="download-cloud" class="mr-1"></span>
              Exporter vers Excel
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Import Card -->
    <div class="col-md-6">
      <div class="card mb-grid h-100">
        <div class="card-header bg-light text-dark">
          <div class="card-header-title">
            <i data-feather="upload" class="mr-2"></i>
            Importer les moyennes
          </div>
        </div>
        <div class="card-body">
          <p class="text-muted">Importez les moyennes d'examen blanc depuis un fichier Excel. Le fichier doit contenir les colonnes dans cet ordre: CODE_ELEVE, NOM_ET_PRENOMS, MOYENNE_EXAMEN_BLANC.</p>
          <form action="{% url 'mock_exam_import' %}?exam={{ selected_exam }}" method="post" enctype="multipart/form-data">
            {% csrf_token %}
            {% if user.role == ROLE_COMMISSION_NATIONALE and locations_list %}
            <div class="form-group">
              <label for="school-export">Localité</label>
                <select name="locations_select" id="locations_select"
                  class="form-control" hx-get="{{ request.path }}?exam={{ selected_exam }}"
                  hx-target="#main-content">
                  <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Toutes les localités</option>
                  {% for location in locations_list %}
                    <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
                  {% empty %}
                  {% endfor %}
                </select>
            </div>
            {% endif %}
            <div class="form-group">
              <label for="school-import">École</label>
              <select name="school" id="school-import" class="form-control" required>
                <option value="">Sélectionner une école</option>
                {% for school in schools %}
                  <option value="{{ school.id }}">{{ school.name }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="form-group">
              <label for="{{ form.excel_file.id_for_label }}">Fichier Excel</label>
              <div class="custom-file">
                {% render_field form.excel_file class="custom-file-input" %}
                <label class="custom-file-label" for="{{ form.excel_file.id_for_label }}">Choisir un fichier</label>
              </div>
            </div>

            <button type="submit" class="btn btn-success btn-block">
              <span data-feather="upload-cloud" class="mr-1"></span>
              Importer et vérifier
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- School Filter (for National Commission) -->
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace();
  }

  // Display filename when selected
  $('.custom-file-input').on('change', function() {
    var fileName = $(this).val().split('\\').pop();
    $(this).next('.custom-file-label').html(fileName);
  });
</script>
