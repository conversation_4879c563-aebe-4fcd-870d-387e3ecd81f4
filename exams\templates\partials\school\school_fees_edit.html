{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">COTISATION ANNUELLE</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Infos cotisation</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.school.id_for_label }}">Ecole *</label>
                {% render_field form.school class='form-control'%}
            </div>
            <div class="form-group mb-2 col-lg-4">
                <label for="{{ form.amount.id_for_label }}">Montant versé</label>
                 {% render_field form.amount class='form-control text-uppercase' %}
            </div>
        </div>

    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>

<script>
    flatpickr("#id_school", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "true",
    });
</script>