{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content shadow-sm" id="modal-content">
    {% csrf_token %}

    <div class="modal-header bg-primary text-white">
        <h5 class="modal-title font-weight-bold">
            <span data-feather="shield" class="feather-18 mr-2"></span>
            GESTION DES PERMISSIONS
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body p-4">
        <!-- School Actions Section -->
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-light d-flex align-items-center">
                <span data-feather="home" class="feather-16 text-primary mr-2"></span>
                <h6 class="mb-0 font-weight-bold text-primary">ACTIONS DES ÉCOLES</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.can_add_student.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="user-plus" class="feather-14 mr-1"></span>
                                Enregistrer des candidats
                            </label>
                            {% render_field form.can_add_student class='form-control' %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.can_edit_student.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="edit" class="feather-14 mr-1"></span>
                                Modifier des candidats
                            </label>
                            {% render_field form.can_edit_student class='form-control' %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Local Commission Actions Section -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light d-flex align-items-center">
                <span data-feather="briefcase" class="feather-16 text-primary mr-2"></span>
                <h6 class="mb-0 font-weight-bold text-primary">ACTIONS DES COMMISSIONS LOCALES</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.can_confirm_student.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="check-circle" class="feather-14 mr-1"></span>
                                Valider des candidats
                            </label>
                            {% render_field form.can_confirm_student class='form-control' %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.can_add_school.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="plus-square" class="feather-14 mr-1"></span>
                                Enregistrer des écoles
                            </label>
                            {% render_field form.can_add_school class='form-control' %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.can_edit_school.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="edit-2" class="feather-14 mr-1"></span>
                                Modifier des écoles
                            </label>
                            {% render_field form.can_edit_school class='form-control' %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.can_edit_grade.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="file-text" class="feather-14 mr-1"></span>
                                Notes Examen National
                            </label>
                            {% render_field form.can_edit_grade class='form-control' %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.can_edit_mock_grade.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="clipboard" class="feather-14 mr-1"></span>
                                Notes Examen Blanc
                            </label>
                            {% render_field form.can_edit_mock_grade class='form-control' %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="{{ form.can_edit_correction.id_for_label }}" class="text-muted font-weight-bold small text-uppercase">
                                <span data-feather="refresh-cw" class="feather-14 mr-1"></span>
                                Demander des corrections
                            </label>
                            {% render_field form.can_edit_correction class='form-control' %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer bg-light">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
            <span data-feather="x" class="feather-14 mr-1"></span> Annuler
        </button>
        <button type="submit" class="btn btn-primary">
            <span data-feather="save" class="feather-14 mr-1"></span> Enregistrer
        </button>
    </div>
</form>