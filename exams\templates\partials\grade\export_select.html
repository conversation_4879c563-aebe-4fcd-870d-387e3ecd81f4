{% load widget_tweaks %}

<form class="row mb-2" method="post" action="{{ action }}" enctype="multipart/form-data">
    {% csrf_token %}
    {% if user.role == ROLE_COMMISSION_NATIONALE %}
    <div class="wrapper col-md-4 mb-2">
      Localité:
      <select name="location" id="location" 
        class="form-control" hx-get="{% url 'room_location_centers' %}" 
        hx-include="[name=exam]"
        hx-target="#center_container">
        <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
        {% for location in locations %}
          <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
        {% empty %}
        {% endfor %}
      </select>
    </div>
    {% endif %}

    <div class="wrapper col-md-4 mb-2">
      Examen:
      <select name="exam" id="exam" 
        class="form-control" hx-get="{% url 'room_location_centers' %}" 
        hx-include="[name=location]"
        hx-target="#center_container">
        {% if selected_exam == 'cepe' %}
        <option value="cepe">CEPE</option>
        {% elif selected_exam == 'bepc' %}
        <option value="bepc">BEPC</option>
        {% elif selected_exam == 'bac' %}
        <option value="bac">BAC</option>
        {% endif %}
      </select>
    </div>
  
  <div class="wrapper col-md-4 mb-2" id="center_container">
    <div>Centre:</div>
    <select name="center" id="center" 
    class="form-control" required="required">
      <option value="0">Sélectionner</option>
      {% for center in centers %}
      <option value="{{ center.id }}" 
      {% if selected_center == center.id %} selected {% endif %}>{{ center }}</option>
      {% empty %}
      {% endfor %}
    </select>
  </div>

  {% if import %}
  <div class="col-md-12 mb-3">
    <div>
      <label for="{{ form.excel_file.id_for_label }}">Fichier Excel *</label>
    </div>
    {% render_field form.excel_file %}
  </div>
  {% endif %}

  <div class="col-md-12">
    <button type="submit" class="btn btn-success">
      <span data-feather="{% if import %}arrow-up{% else %}arrow-down{% endif %}"></span>
      {% if import %} Importer dans l'application {% else %} Exporter vers Excel {% endif %}
    </button>
  </div>
</form>