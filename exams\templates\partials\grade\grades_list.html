{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}"
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Notes</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">ETAT DES SAISIES DES NOTES PAR CENTRE</h5>
  </div>
  <div class="mb-3">
    <p>Total saisies Examen Blanc: <strong>{{ mock_exam_marked }} / {{ candidates }}</strong></p>
    <p>Total saisies Examen National: <strong>{{ national_exam_marked }} / {{ candidates }}</strong></p>
  </div>

  {% if perms.exams.edit_grade %}
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
    <div>
      <a class="btn btn-success text-white"
          hx-get="{% url 'grade_add' %}"
          hx-target="#main-content">
          + Commencer la saisie</a>
      <a class="btn btn-info text-white ml-2"
          hx-get="{% url 'students_grades_status' %}"
          hx-push-url="{% url 'students_grades_status' %}"
          hx-target="#main-content">
          <span data-feather="list"></span> Etat des saisies par élève</a>
    </div>
  </div>
  {% endif %}


    <div class="row d-flex justify-content-center my-2 {% if not user.role == ROLE_COMMISSION_NATIONALE %} d-none {% endif %}">
      <div class="wrapper col-md-6">
        Localité:
        <select name="locations" id="locations"
          class="form-control" hx-get="{% url 'grades' %}"
          hx-target="#main-content">
          <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
          {% for location in locations_list %}
            <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
          {% empty %}
          {% endfor %}
        </select>
      </div>
    </div>

    {% include 'components/search_and_page_size.html' with include_items='[name=locations]' %}
  <div class="row" id="table-wrapper">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0" data-table>
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">CENTRE</th>
                <th scope="col" class="text-white">EXAMEN BLANC</th>
                <th scope="col" class="text-white">EXAMEN NATIONAL</th>
                <th scope="col" class="text-white">Statut</th>
              </tr>
            </thead>
            <tbody>
              {% for center in centers %}
              <tr>
                <td><a href="" hx-get="{% url 'students_grades_status' %}?center={{ center.id }}" hx-target="#main-content">{{ center }}</a></td>
                <td>{{ center.candidates_marked_mock_exam }} / {{ center.candidates }}</td>
                <td>{{ center.candidates_marked_gen_exam }} / {{ center.candidates }}</td>
                <td><span class="badge {% if center.candidates_marked_mock_exam == center.candidates and center.candidates_marked_gen_exam == center.candidates %} badge-success {% else %} badge-warning {% endif %}">
                  {% if center.candidates_marked_mock_exam == center.candidates and center.candidates_marked_gen_exam == center.candidates %} TERMINE {% else %} EN COURS {% endif %}
                </span></td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items='[name=search], [name=locations]' %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace();
  }
</script>