from django.shortcuts import redirect
from django.urls import reverse, NoReverseMatch
from django.contrib import messages
from project_utils import constants, custom_utils
from .wizard_views import is_school_stats_completed


class SchoolInformationMiddleware:
    """
    Middleware to ensure school users complete their information before accessing other pages.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        # One-time configuration and initialization.

    def __call__(self, request):
        # Code to be executed for each request before
        # the view (and later middleware) are called.

        try:
            # Check if user is authenticated and has school role
            if (request.user.is_authenticated and
                hasattr(request.user, 'role') and
                request.user.role == constants.ROLE_ECOLE and
                hasattr(request.user, 'school') and
                request.user.school):

                school = request.user.school

                # Check if school needs to complete any step
                from . import models
                has_fees_record = False
                try:
                    models.SchoolFees.objects.get(school=school)
                    has_fees_record = True
                except models.SchoolFees.DoesNotExist:
                    has_fees_record = False

                year = custom_utils.get_selected_year(request)
                stats_updated = is_school_stats_completed(school, year)

                # Only redirect if something is incomplete
                
                if year.stats_required and \
                    (not year.stats_required_schools.exists() or school in year.stats_required_schools.all()) and \
                    (not school.information_updated or
                    not stats_updated or
                    not has_fees_record):

                    # Define allowed URLs
                    try:
                        allowed_urls = [
                            reverse('school_information_check'),
                            reverse('school_information_wizard'),
                            reverse('logout'),
                            '/admin/',  # Allow admin access
                        ]
                    except NoReverseMatch:
                        # If URL patterns are not available yet, skip middleware
                        response = self.get_response(request)
                        return response

                    # Check if current path is allowed
                    current_path = request.path
                    is_allowed = any(current_path.startswith(url) for url in allowed_urls)

                    # Also allow static files and media files
                    if (current_path.startswith('/static/') or
                        current_path.startswith('/media/') or
                        current_path.startswith('/favicon.ico')):
                        is_allowed = True

                    # If not allowed, redirect to information check page
                    if not is_allowed:
                        # Only show warning message for incomplete info/stats
                        if not school.information_updated or not stats_updated:
                            messages.warning(
                                request,
                                "Veuillez compléter les informations de votre école avant de continuer."
                            )
                        return redirect('school_information_check')

        except Exception:
            # If any error occurs, let the request continue normally
            pass

        response = self.get_response(request)

        # Code to be executed for each request/response after
        # the view is called.

        return response
