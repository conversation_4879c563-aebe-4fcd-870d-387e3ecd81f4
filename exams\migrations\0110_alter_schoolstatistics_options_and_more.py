# Generated by Django 4.2.1 on 2025-07-31 15:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0109_school_information_updated_school_school_status_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='schoolstatistics',
            options={'verbose_name': "Statistiques d'école", 'verbose_name_plural': "Statistiques d'écoles"},
        ),
        migrations.AlterField(
            model_name='school',
            name='school_status',
            field=models.CharField(choices=[('RC', "Réconnue par l'Etat"), ('NR', "Non réconnue par l'Etat")], max_length=2, null=True, verbose_name="Statut de l'école"),
        ),
        migrations.AlterField(
            model_name='school',
            name='school_type',
            field=models.CharField(choices=[('ECC', 'Ecole Confessionnelle CHERIFLA'), ('ESIA', 'Etablissement Scolaire Islamique Affiliée')], max_length=4, null=True, verbose_name="Choisir le type d'école"),
        ),
        migrations.Alter<PERSON>ield(
            model_name='school',
            name='teachers_count',
            field=models.PositiveSmallIntegerField(null=True),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='boys_count',
            field=models.PositiveSmallIntegerField(null=True, verbose_name='nombre de garçons'),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='girls_count',
            field=models.PositiveSmallIntegerField(null=True, verbose_name='nombre de filles'),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='levels_count',
            field=models.PositiveSmallIntegerField(null=True, verbose_name='nombre de classes'),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='students_count',
            field=models.PositiveSmallIntegerField(null=True, verbose_name="nombre total d'élèves"),
        ),
        migrations.AlterUniqueTogether(
            name='schoolstatistics',
            unique_together={('school', 'year')},
        ),
    ]
