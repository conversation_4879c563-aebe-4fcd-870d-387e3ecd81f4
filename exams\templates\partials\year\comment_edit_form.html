{% extends 'components/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %} Message défilant {% endblock %}

{% block modal_body %}
    {% csrf_token %}
    
    <div class="modal-body mx-2">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Définir un message à afficher </div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-12 mx-2">
                <label for="{{ form.school_message.id_for_label }}">Messages pour écoles</label>
                {% render_field form.school_message class='form-control' rows='4' %}
            </div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-lg-12 mt-2">
                <label for="{{ form.commissions_message.id_for_label }}">Messages pour Commissions Locales</label>
                {% render_field form.commissions_message class='form-control' rows='4' %}
            </div>
        </div>
    </div>
{% endblock %}

{% block modal_footer %}
    <button type="submit" class="btn btn-success">Enregistrer</button>
{% endblock %}