# Generated by Django 4.2.1 on 2025-07-31 14:25

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0108_correctioncenter_locations'),
    ]

    operations = [
        migrations.AddField(
            model_name='school',
            name='information_updated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='school',
            name='school_status',
            field=models.CharField(blank=True, choices=[('RC', "Réconnue par l'Etat"), ('NR', "Non réconnue par l'Etat")], max_length=2, null=True, verbose_name="Statut de l'école"),
        ),
        migrations.AddField(
            model_name='school',
            name='school_type',
            field=models.CharField(blank=True, choices=[('ECC', 'Ecole Confessionnelle CHERIFLA'), ('ESIA', 'Etablissement Scolaire Islamique Affiliée')], max_length=4, null=True, verbose_name="Choisir le type d'école"),
        ),
        migrations.AddField(
            model_name='school',
            name='stats_updated',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='school',
            name='teachers_count',
            field=models.PositiveSmallIntegerField(default=0),
        ),
        migrations.CreateModel(
            name='SchoolStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_created', models.DateTimeField(default=django.utils.timezone.now)),
                ('date_updated', models.DateTimeField(default=django.utils.timezone.now)),
                ('levels_count', models.PositiveSmallIntegerField(verbose_name='nombre de classes')),
                ('students_count', models.PositiveSmallIntegerField(verbose_name="nombre total d'élèves")),
                ('boys_count', models.PositiveSmallIntegerField(verbose_name='nombre de garçons')),
                ('girls_count', models.PositiveSmallIntegerField(verbose_name='nombre de filles')),
                ('school', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='exams.school')),
                ('year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='exams.year')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
