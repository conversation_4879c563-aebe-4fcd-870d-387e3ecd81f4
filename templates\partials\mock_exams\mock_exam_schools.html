{% load static %}
{% load humanize %}

<div class="container-fluid mt-2" hx-get="{{ request.path }}" hx-target="#main-content" hx-trigger="saved from:body">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}"
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Listing des écoles - Examen Blanc</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      LISTING DES ÉCOLES - EXAMEN BLANC
    </h5>
  </div>

  <div class="d-flex justify-content-between row mb-2 {% if user.role == ROLE_ECOLE %}d-none{% endif %}">
    <div class="wrapper col-6 {% if not user.role == ROLE_COMMISSION_NATIONALE %} d-none {% endif %}">
      Localité:
      <select name="locations_select" id="locations_select"
        class="form-control" hx-get="{{ request.path }}?exam={{ exam }}"
        hx-target="#main-content">
        <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
        {% for location in locations_list %}
          <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
        {% empty %}
        {% endfor %}
      </select>
    </div>
  </div>

  {% include 'components/search_and_page_size.html' with include_items='[name=locations_select], [name=search], [name=per_page]'%}
  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">CODE</th>
                <th scope="col" class="text-white">ÉCOLE</th>
                <th scope="col" class="text-white">CENTRE</th>
                <th scope="col" class="text-white">CANDIDATS</th>
                <th scope="col" class="text-white">NOTÉS</th>
                <th scope="col" class="text-white">Fiche de notation</th>
              </tr>
            </thead>
            <tbody>
              {% for school in schools %}
              <tr>
                <td>{{ school.identifier }}</td>
                <td>{{ school.name|upper }}</td>
                <td>
                  {% if school.center_name %}
                    <span class="badge badge-pill badge-success">{{ school.center_name }}</span>
                  {% else %}
                    <span class="badge badge-pill badge-warning">Non assigné</span>
                  {% endif %}
                </td>
                <td>{{ school.candidates_count|default:'0' }}</td>
                <td>{{ school.candidates_marked_mock_exam|default:'0' }}</td>
                <td>
                  <a href="{% url 'mock_exam_school_pdf' school.id exam %}" class="btn btn-sm btn-outline-primary">
                    <span data-feather="file-text"></span> Imprimer
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items='[name=search], [name=per_page], [name=locations_select]' %}
        </div>
      </div>
    </div>
  </div>
</div>
