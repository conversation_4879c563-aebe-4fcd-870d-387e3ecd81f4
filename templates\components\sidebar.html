<div class="adminx-sidebar expand-hover push" id="sidebar">
    <ul class="sidebar-nav">
      <li class="sidebar-nav-item sidebar-navitem-close-on-click">
        <a href="" 
           hx-get="{% url 'home' %}" 
           hx-target="#main-content" 
           hx-push-url="{% url 'home' %}"
           class="sidebar-nav-link {% if section == 'home' %}active{% endif %}"
           >
          <span class="sidebar-nav-icon"><i data-feather="home"></i></span>
          <span class="sidebar-nav-name">Accueil</span>
          <span class="sidebar-nav-end"></span>
        </a>
      </li>
      
      {% if perms.exams.view_school or perms.exams.view_localcommission %}
      <li class="sidebar-nav-item">
        <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
          <span class="sidebar-nav-icon"><i data-feather="briefcase"></i></span>
          <span class="sidebar-nav-name">Structures</span>
          <span class="sidebar-nav-end">
            <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
          </span>
        </a>
        <ul class="sidebar-nav-submenu" style="display: none;">
          {% if perms.exams.view_location %}
          <li class="sidebar-nav-item sidebar-navitem-close-on-click">
            <a hx-get="{% url 'drena' %}" hx-push-url="{% url 'drena' %}" hx-target="#main-content"
               class="sidebar-nav-link {% if section == 'drena' %}active{% endif %}">
              <span data-feather="circle" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2">Liste des DRENA</span>
            </a>
          </li>
          {% endif %}
          {% if perms.exams.view_school %}
          <li class="sidebar-nav-item sidebar-navitem-close-on-click">
            <a href="" 
               hx-get="{% url 'schools' %}" 
               hx-target="#main-content"
               hx-push-url="{% url 'schools' %}"  
               class="sidebar-nav-link {% if section == 'schools' %}active{% endif %}">
              <span data-feather="grid" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2">Ecoles</span>
            </a>
          </li>
          {% endif %}
          
          {% if perms.exams.change_localcommission %}
          <li class="sidebar-nav-item sidebar-navitem-close-on-click">
            <a href="" 
               hx-get="{% url 'commissions' %}" 
               hx-target="#main-content"
               hx-push-url="{% url 'commissions' %}"  
               class="sidebar-nav-link {% if section == 'commissions' %}active{% endif %}">
              <span data-feather="link" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2">Commissions Locales</span>
            </a>
          </li>
          {% endif %}
        </ul>
      </li>
      {% endif %}

      {% include 'components/exam_menu.html' with exam_name='cepe' %}
      <!-- BEPC -->
      {% include 'components/exam_menu.html' with exam_name='bepc' %}

        <!-- Start -->


      <li class="sidebar-nav-item" style="list-style-type: none;">
        <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
          <span class="sidebar-nav-icon"><i data-feather="dollar-sign"></i></span>
          <span class="sidebar-nav-name">Comptabilité</span>
          <span class="sidebar-nav-end">
            <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
          </span>
        </a>
  
        <!-- Third Level -->
        {% if perms.exams.view_schoolpayment %}
        <ul class="sidebar-nav-submenu" style="display: none;">
          {% if perms.exams.add_schoolpayment %}
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'pricing_update' request.session.selected_school_year_id %}"
                hx-target="#dialog"
                class="sidebar-nav-link {% if section == 'settings' %}active{% endif %}">
               <span data-feather="dollar-sign" class="feather-16"></span>
               <span class="sidebar-nav-name ml-2"> Tarifs des Examens</span>
             </a>
           </li>
           {% endif %}
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'school_fees' %}" 
             hx-target="#main-content"
             hx-push-url="{% url 'school_fees' %}"
             class="sidebar-nav-link {% if section == 'payments' %} active {% endif %}">
                <span data-feather="command" class="feather-16"></span>
                <span class="sidebar-nav-name ml-2">Paiement Cotisation Annuelle Ecole</span>
             </a>
           </li>
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'payments' %}"
              hx-target="#main-content"
              hx-push-url="{% url 'payments' %}"
              class="sidebar-nav-link {% if section == 'payments' %} active {% endif %}">
                <span data-feather="layers" class="feather-16"></span>
                <span class="sidebar-nav-name ml-2"> Paiement Droits d'Examen</span>
             </a>
           </li>
          </ul>
        </li>
        {% endif %}

      <!-- End -->
      {% if perms.exams.view_transferrequest %}
      <li class="sidebar-nav-item sidebar-navitem-close-on-click">
        <a href="" hx-get="{% url 'transferts' %}" 
           hx-target="#main-content"
           hx-push-url="{% url 'transferts' %}"
           class="sidebar-nav-link {% if section == 'transferts' %} active {% endif %}">
          <span class="sidebar-nav-icon"><i data-feather="arrow-up-right"></i></span>
          <span class="sidebar-nav-name">Transferts</span>
          <span class="sidebar-nav-end"></span>
        </a>
      </li>
      {% endif %}
      {% if perms.exams.add_localcommission or perms.exams.view_year %}
      <li class="sidebar-nav-item">
        <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
          <span class="sidebar-nav-icon"><i data-feather="settings"></i></span>
          <span class="sidebar-nav-name">Paramètres</span>
          <span class="sidebar-nav-end">
            <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
          </span>
        </a>
        <ul class="sidebar-nav-submenu" style="display: none;">
          {% if perms.exams.add_localcommission %}
          <li class="sidebar-nav-item sidebar-navitem-close-on-click">
            <a href="" 
               hx-get="{% url 'year_permissions' %}" 
               hx-target="#main-content"
               hx-push-url="{% url 'year_permissions' %}"
               class="sidebar-nav-link {% if section == 'actions' %} active {% endif %}">
              <span data-feather="alert-triangle" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2">Autorisations et Privilèges</span>
            </a>
          </li>
          {% endif %}
          
          {% if perms.exams.view_year %}
          <li class="sidebar-nav-item sidebar-navitem-close-on-click">
            <a href=""
               hx-get="{% url 'message_update' request.session.selected_school_year_short %}"
               hx-target="#dialog"
               class="sidebar-nav-link">
              <span data-feather="info" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2">Message perso</span>
            </a>
          </li>
          {% endif %}
        </ul>
      </li>
      {% endif %}
      {% if perms.exams.view_subject %}
      <li class="sidebar-nav-item sidebar-navitem-close-on-click">
        <a href="" hx-get="{% url 'subjects' %}?exam=cepe" 
           hx-target="#main-content"
           hx-push-url="{% url 'subjects' %}?exam=cepe"
           class="sidebar-nav-link {% if section == 'subjects' %} active {% endif %}">
          <span class="sidebar-nav-icon"><i data-feather="list"></i></span>
          <span class="sidebar-nav-name">Matières</span>
          <span class="sidebar-nav-end"></span>
        </a>
      </li>
      {% endif %}


      <!-- <li class="sidebar-nav-item sidebar-navitem-close-on-click">
        <a href="" 
           hx-get="{% url 'students_cards' %}?niveau=AUTRES" 
           hx-target="#main-content" 
           hx-push-url="{% url 'students_cards' %}?niveau=AUTRES"
           class="sidebar-nav-link {% if section == 'student_cards' %}active{% endif %}"
           >
          <span class="sidebar-nav-icon"><i data-feather="credit-card"></i></span>
          <span class="sidebar-nav-name">Cartes scolaires</span>
          <span class="sidebar-nav-end"></span>
        </a>
      </li> -->

      <li class="sidebar-nav-item" style="list-style-type: none;">
        <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
          <span class="sidebar-nav-icon"><i data-feather="credit-card"></i></span>
          <span class="sidebar-nav-name">Cartes scolaires</span>
          <span class="sidebar-nav-end">
            <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
          </span>
        </a>
  
        <ul class="sidebar-nav-submenu" style="display: none;">
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a hx-get="{% url 'students_cards' %}?niveau=AUTRES" hx-push-url="{% url 'students_cards' %}?niveau=AUTRES" hx-target="#main-content"
                class="sidebar-nav-link {% if section == 'student_cards' %}active{% endif %}"
                hx-target="#main-content">
               <span data-feather="list" class="feather-16"></span>
               <span class="sidebar-nav-name ml-2">Liste des demandes</span>
             </a>
           </li>
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'students_cards' %}?niveau=AUTRES&filtre=PRODUITES"
             hx-target="#main-content"
             hx-push-url="{% url 'students_cards' %}?niveau=AUTRES&filtre=PRODUITES"
             class="sidebar-nav-link {% if section == 'users' %} active {% endif %}">
                <span data-feather="check-circle" class="feather-16"></span>
                <span class="sidebar-nav-name ml-2">Cartes Produites</span>
             </a>
           </li>
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'students_cards' %}?niveau=AUTRES&filtre=LIVREES"
             hx-target="#main-content"
             hx-push-url="{% url 'students_cards' %}?niveau=AUTRES&filtre=LIVREES"
             class="sidebar-nav-link {% if section == 'staff' %} active {% endif %}">
                <span data-feather="truck" class="feather-16"></span>
                <span class="sidebar-nav-name ml-2">Cartes Livrées</span>
             </a>
           </li>
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'students_cards' %}?niveau=AUTRES&filtre=DEMANDES"
             hx-target="#main-content"
             hx-push-url="{% url 'students_cards' %}?niveau=AUTRES&filtre=DEMANDES"
             class="sidebar-nav-link {% if section == 'staff' %} active {% endif %}">
                <span data-feather="x-circle" class="feather-16"></span>
                <span class="sidebar-nav-name ml-2">Cartes Non Produites</span>
             </a>
           </li>
          </ul>
        </li>

      {% if perms.exams.view_location and user.role == 'CN' or user.is_staff %}
      <li class="sidebar-nav-item" style="list-style-type: none;">
        <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
          <span class="sidebar-nav-icon"><i data-feather="user"></i></span>
          <span class="sidebar-nav-name">Ressources Humaines</span>
          <span class="sidebar-nav-end">
            <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
          </span>
        </a>
  
        <!-- Third Level -->
        <ul class="sidebar-nav-submenu" style="display: none;">
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'user_list' %}" 
             hx-target="#main-content"
             hx-push-url="{% url 'user_list' %}"
             class="sidebar-nav-link {% if section == 'users' %} active {% endif %}">
                <span data-feather="circle" class="feather-16"></span>
                <span class="sidebar-nav-name ml-2">Délégués</span>
             </a>
           </li>
           <li class="sidebar-nav-item sidebar-navitem-close-on-click">
             <a href="" hx-get="{% url 'staff' %}"
             hx-target="#main-content"
             hx-push-url="{% url 'staff' %}"
             class="sidebar-nav-link {% if section == 'staff' %} active {% endif %}">
                <span data-feather="circle" class="feather-16"></span>
                <span class="sidebar-nav-name ml-2">Personnel</span>
             </a>
           </li>
          </ul>
        </li>
        {% endif %}
    </ul>
  </div>

  <script>
    
    function toggleSubmenu(element) {
    // Get the parent li element
    const menuItem = element.closest('.sidebar-nav-item');
    if (!menuItem) return;
    
    // Find the submenu within this menu item
    const submenu = menuItem.querySelector('.sidebar-nav-submenu');
    if (!submenu) return;
    
    // Find the chevron icon within the clicked link
    const chevron = element.querySelector('.feather-chevron-right');
    
    // Get all submenus at the same level
    const parentUl = menuItem.parentElement;
    const siblingMenuItems = parentUl.querySelectorAll(':scope > .sidebar-nav-item');
    
    siblingMenuItems.forEach(sibling => {
        if (sibling !== menuItem) {
            // Close sibling submenu
            const siblingSubmenu = sibling.querySelector('.sidebar-nav-submenu');
            const siblingChevron = sibling.querySelector('.feather-chevron-right');
            
            if (siblingSubmenu) {
                siblingSubmenu.style.display = "none";
            }
            if (siblingChevron) {
                siblingChevron.style.transform = "rotate(0deg)";
                siblingChevron.style.transition = ".3s ease";
            }
        }
    });
    
    // Toggle clicked submenu
    if (submenu.style.display === "none" || !submenu.style.display) {
        submenu.style.display = "block";
        if (chevron) {
            chevron.style.transform = "rotate(90deg)";
            chevron.style.transition = ".3s ease";
        }
    } else {
        submenu.style.display = "none";
        if (chevron) {
            chevron.style.transform = "rotate(0deg)";
        }
    }
    
    // Re-render Feather icons if needed
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
}

    function updateActiveMenuState() {
        // Remove all previous active parent states
        document.querySelectorAll('.has-active-submenu').forEach(item => {
            item.classList.remove('has-active-submenu');
        });

        // Find current active submenu item
        const activeSubmenuItem = document.querySelector('.sidebar-nav-submenu .sidebar-nav-link.active');
        if (activeSubmenuItem) {
            // Add active state to parent menu item
            const parentMenuItem = activeSubmenuItem.closest('.sidebar-nav-item');
            if (parentMenuItem) {
                parentMenuItem.classList.add('has-active-submenu');
            }
        }
    }

    // Call this function when page loads and after any navigation
    document.addEventListener('DOMContentLoaded', updateActiveMenuState);
    // If using HTMX, also add:
    document.body.addEventListener('htmx:afterSettle', updateActiveMenuState);
  </script>
