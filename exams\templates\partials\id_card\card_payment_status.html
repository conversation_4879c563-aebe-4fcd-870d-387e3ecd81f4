<span id="id_{{ card.id }}" hx-swap="outerHTML" class="badge badge-pill p-1 {% if card.card_payment_status == 'UP' %} badge-warning {% else %} badge-success {% endif %}">{{ card.get_card_payment_status_display|upper }}</span>
<a href="" hx-swap-oob="true" id="btn_{{ card.id }}" hx-swap="outerHTML" hx-get="{% url 'student_card_payment_status' card.id %}" hx-target="#id_{{ card.id }}" class="btn {% if card.card_payment_status == 'UP' %} btn-outline-success {% else %} btn-outline-danger {% endif %} btn-sm" title="{% if card.card_payment_status == 'UP' %} Marquer comme payé {% else %} Marquer comme non payé {% endif %}">
    <span data-feather="check"></span>
</a>