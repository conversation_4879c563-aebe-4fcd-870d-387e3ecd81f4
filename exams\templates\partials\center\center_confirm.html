{% extends 'components/modal.html' %}

{% block modal_title %}Confirmer tous les centres créer ?{% endblock %}

{% block modal_body %}
    <div class="modal-body">
        <input type="hidden" name="exam" value="{{ exam }}">
        Vous êtes sur le point de <span class="font-weight-bold text-danger">CONFIRMER</span>
        <span class="text-muted"> tous les centres crées pour l'examen du <span class="font-weight-bold text-danger">{{ exam|upper }}</span>:</span> 
        <br>
        1. Cette action va confirmer que toutes les salles sont crées <br>
        2. La capacité des salles peut contenir les candidats validés <br>
        3. Les candidats seront affectés aux salles <br>

        Confirmer ?
    </div>
{% endblock %}

{% block modal_footer %}
    <button type="submit" class="btn btn-success" id="confirmBtn">Confirmer</button>
    <script>
        document.getElementById('modal-content').addEventListener('submit', function() {
            var button = document.getElementById('confirmBtn');
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Traitement...';
        });
    </script>
{% endblock %}

