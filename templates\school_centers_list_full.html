{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        {% include 'partials/school/school_centers_list.html' %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Ensure search works properly
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Focus search input after page loads if it had focus before
        if (document.activeElement && document.activeElement.id === 'search-input') {
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                const val = searchInput.value;
                searchInput.focus();
                searchInput.value = '';
                searchInput.value = val;
            }
        }
    });
</script>
{% endblock %}
