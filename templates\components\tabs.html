<ul class="nav nav-tabs" id="tab" role="tablist">
    {% for item in nav_items %}
    <li class="nav-item">
      <a class="nav-link {% if level == item.code %}active{% endif %}" id="home-tab" data-toggle="tab" href="#home" role="tab" 
         aria-controls="home" aria-selected="false" hx-get="{% url 'students_cards' %}?niveau={{ item.code }}&filtre={{ request.GET.filtre }}" hx-target="#main-content">{{ item.title }}</a>
    </li>
    {% endfor %%}
</ul>

<div class="tab-content mb-grid pt-3 material-form" id="tab-content">
    <div class="tab-pane fade active show" id="tab-content" role="tabpanel" aria-labelledby="tab">
      <div class="form-group row">
        <div class="col-8">
          <label for="search" class="pl-2">Rechercher:</label>
          <input type="search" name="search" id="search" 
                  class="form-control btn-sm ml-2"
                  value="{{ search }}"
                  hx-get="{{ request.path }}?niveau={{ level }}&filtre={{ request.GET.filtre }}" hx-target="#main-content">
        </div>
        <div class="col-4">
          <label for="search" class="pl-2">Afficher</label>
          <select name="per_page" id="per_page" class="form-control form-control-sm" hx-get="{{ request.path }}?page={{ page }}&niveau={{ level }}&filtre={{ request.GET.filtre }}"
                  hx-target="#main-content">
            <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 éléments</option>
            <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 éléments</option>
            <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 éléments</option>
            <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 éléments</option>
          </select> 
        </div>
      </div>
    
      {% if result_found %}
      <div class="alert alert-warning font-weight-bold d-flex justify-content-between border">
        <span><span data-feather="filter" class="feather-16 align-middle mr-2"></span> {{ result_found }} résultat(s)</span>
      </div>
      {% endif %}

        <div class="row">
            <div class="col">
              <div class="card mb-grid">
                <div class="table-responsive-md">
                  <table class="table table-sm table-actions table-striped table-hover mb-0" style="font-size: 12px;">
                    <thead class="bg-secondary">
                      <tr>
                        <th scope="col" class="text-white">PHOTO</th>
                        <th scope="col" class="text-white">MATRICULE <br> OU ID</th>
                        <th scope="col" class="text-white">NOM ET PRENOMS</th>
                        <th scope="col" class="text-white">CLASSE</th>
                        <th scope="col" class="text-white">STATUT</th>
                        <th scope="col" class="text-white">PAIEMENT</th>
                        <th scope="col" class="text-white">DRENA</th>
                        <th scope="col" class="text-white">ECOLE</th>
                        <th scope="col" class="text-white">NE LE</th>
                        <th scope="col" class="text-white">{% if request.GET.filtre == 'DEMANDES' %} Commentaire {% else %} Contact {% endif %}</th>
                        <th scope="col" class="text-white">DEMANDE LE</th>
                        <th scope="col" class="text-white align-left">ACTIONS</th>
                        <th scope="col" class="text-white">CONVERTIR<br>EN CANDIDAT</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for student in data %}
                      <tr>
                        <td class="align-middle text-center" style="min-width: 60px;">
                          {% if student.photo %}
                            <img data-original="{{ student.photo.url }}" 
                                alt="1" 
                                class="lazy border img-thumbnail rounded-circle">
                          {% endif %}
                        </td>
                        <td class="align-middle"><strong>{% if student.matricule_dsps %} {{ student.matricule_dsps|default_if_none:'-' }} {% else %} {{ student.matricule_cherifla }} {% endif %}</strong></td>
                        <td class="align-middle">{{ student|upper }}</td>
                        <td class="align-middle">{{ student.get_level_display }}</td>
                        <td class="align-middle" > 
                          <span class="badge badge-pill p-1 {% if student.status == 'P' %} badge-warning {% elif student.status == 'M' %} badge-info {% elif student.status == 'S' %} badge-success {% endif %}">{{ student.get_status_display }}</span>
                        </td>
                        <td class="align-middle"> 
                          <span id="id_{{ student.id }}" hx-swap="outerHTML" class="badge badge-pill p-1 {% if student.card_payment_status == 'UP' %} badge-warning {% else %} badge-success {% endif %}">{{ student.get_card_payment_status_display|upper }}</span>
                        </td>
                        <td class="align-middle">{% if student.school.drena %} {{ student.school.drena }} {% else %} - {% endif %}</td>
                        <td class="align-middle">{{ student.school }}</td>
                        <td class="align-middle">{{ student.birth_date_str }}</td>
                        <td class="align-middle">{% if request.GET.filtre == 'DEMANDES' %} {% if student.comment %} <span class="text-danger">{{ student.comment }}</span> {% else %} - {% endif %} {% else %} {{ student.phone }} {% endif %} </td>
                        <td class="align-middle">{{ student.created_at_str }}</td>
                        <td style="min-width: 100px;" class="align-middle">
                          <a id="btn_{{ student.id }}" href="" hx-swap="outerHTML" hx-get="{% url 'student_card_payment_status' student.id %}" hx-target="#id_{{ student.id }}" class="btn {% if student.card_payment_status == 'UP' %} btn-outline-success {% else %} btn-outline-danger {% endif %} btn-sm" title="{% if student.card_payment_status == 'UP' %} Marquer comme payé {% else %} Marquer comme non payé {% endif %}">
                            <span data-feather="check"></span>
                          </a>
                          <a href="" hx-get="{% url 'student_card_edit' student.id %}" hx-target="#dialog" title="Modifier" class="btn btn-warning btn-sm {% if student.status != 'P' %} disabled {%endif %} ">
                            <span data-feather="edit"></span>
                          </a>
                          <a href="" hx-get="{% url 'student_card_delete' student.id %}" hx-target="#dialog" title="Supprimer" class="btn btn-danger btn-sm">
                            <span data-feather="trash"></span>
                          </a>
                        </td>
                        <td class="align-middle">
                          <a href="" hx-get="{% url 'student_card_convert' student.id %}" hx-target="#dialog" class="btn btn-info btn-sm w-100 text-center" title="Convertir en candidat à l'examen">
                            <span data-feather="corner-left-up"></span>
                          </a>
                        </td>
                      </tr>
                      {% endfor %}
                    </tbody>
                  </table>
                  {% include 'components/pagination.html' with include_items="[name=per_page]" vals_items=True %}
                </div>
              </div>
            </div>
          </div>
    </div>
</div>

<script>
  if (typeof(feather) != "undefined") {
    feather.replace()
  };

  $('img.lazy').lazyload({
      load: function() { $(this).removeClass("lazyload"); },
    });

    // Listen to htmx after request
    document.body.addEventListener('htmx:afterRequest', function(event) {
      if (typeof(feather) != "undefined") {
        feather.replace()
      };
    });

</script>