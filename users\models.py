from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from exams import models as exam_models
from project_utils import constants

class CustomUser(AbstractUser):
    USER_ROLES = (
        (constants.ROLE_ECOLE, _('Ecole')),
        (constants.ROLE_COMMISSION_LOCALE, _('Commission Locale Examen')),
        (constants.ROLE_COMMISSION_NATIONALE, _('Commission Nationale Examen')),
        (constants.ROLE_DELEGATE, _('Délégué pour carte scolaire')),
    )
    school = models.ForeignKey(exam_models.School, null=True, blank=True, 
                               on_delete=models.CASCADE)
    role = models.CharField(
        _('role'), max_length=2, choices=USER_ROLES, 
        default=constants.ROLE_ECOLE)
    gender = models.CharField(_('sexe'), max_length=1, 
        choices=constants.GENDER_CHOICES, default=constants.GENDER_MALE,
        null=True, blank=True)
    initial_password = models.Char<PERSON>ield(_('mot de passe initial'), null=True, 
                                        blank=True, max_length=50)
    locations = models.ManyToManyField(
        exam_models.Location, blank=True, 
        verbose_name=_('drena pris en charge'))

    def get_locations(self):
        return ', '.join([loc.name for loc in self.locations.all()])