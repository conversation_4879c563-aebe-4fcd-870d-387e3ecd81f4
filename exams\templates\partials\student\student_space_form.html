{% load static %}
{% load widget_tweaks %}

    <form action="{{ request.path }}" method="post" class="text-center mt-3" id="result">
        {% csrf_token %}
        <div>
        <h5 class="mt-4 font-weight-bold">{{ description }}</h5>
        </div>
        <div class="form-group mb-3">
            <label for="{{ form.identifier.id_for_label }}">Numéro d'inscription ou numéro de table</label>
            {% render_field form.identifier class='form-control my-1' %}
        </div>
        <div class="wrapper mb-2">
            {% if infos %}
            <a class="btn btn-block btn-primary text-white"
               hx-get="{% url 'candidate_infos' %}"
               hx-include="[name=identifier]"
               hx-target="#result"><span data-feather="eye"></span>Vérifier</a>
            {% elif result %}
               <a class="btn btn-block btn-primary text-white"
                  hx-post="{% url 'resultat_candidat' %}"
                  hx-include="[name=identifier]"
                  hx-target="#result"><span data-feather="eye"></span>Consulter résultat</a>
            {% else %}
            <button type="submit" class="btn btn-block btn-success text-white"><span data-feather="file-text"></span> {{ button_text }}</button>
            {% endif %}
        </div>

        <div class="text-center mt-3" id="result"></div>
    </form>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace();
  }
</script>