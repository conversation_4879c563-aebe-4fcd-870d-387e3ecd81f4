{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item"><a href="#">Accueil</a></li>
      <li class="breadcrumb-item active" aria-current="page">Elèves {{ selected_year }}</li>
    </ol>
  </nav>

  {% if enrollment_closed %}
  <div class="mx-2">
    <div class="alert alert-warning" role="alert">
      <span data-feather="alert-triangle"></span>
      <small>Les inscriptions des candidats sont fermées</small>
    </div>
  </div>
  {% endif %}

  <div class="pb-3 mt-3">
    <h3 class="fw-bold">Demandes de transfert
    </h3>
  </div>

  <div class="d-flex flex-row justify-content-between mb-3">
    {% if perms.exams.add_student %}
        <a class="btn btn-success text-white"
           hx-get=""
           hx-target="#dialog">
           <i data-feather="plus"></i>
           Ajouter</a>
    {% endif %}
</div>

    <div class="d-flex justify-content-between row mb-2 {% if user.role == ROLE_ECOLE %}d-none{% endif %}">
      <div class="wrapper col-6 {% if not user.role == ROLE_COMMISSION_NATIONALE %} d-none {% endif %}">
        Localité:
        <select name="locations_select" id="locations_select" 
          class="form-control" hx-get="" 
          hx-target="#main-content">
          <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
          {% for location in locations_list %}
            <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
          {% empty %}
          {% endfor %}
        </select>
      </div>
      
      <div class="wrapper col-6 {% if user.role == ROLE_ECOLE %} d-none {% endif %}">
        Ecole:
        <select name="schools_select" id="schools_select" 
        class="form-control"
        hx-get="" hx-target="#main-content" hx-include="[name=locations_select]">
          <option value="0">Sélectionner</option>
          {% for school in schools_list %}
          <option value="{{ school.id }}" 
          {% if selected_school == school.id %} selected {% endif %}>{{ school }}</option>
          {% empty %}
          {% endfor %}
        </select>
      </div>
    </div>

    <div class="form-group row">
      <div class="col-8">
        <label for="search" class="pl-2">Rechercher:</label>
        <input type="search" name="search" id="search" 
                class="form-control btn-sm ml-2"
                value="{{ search }}"
                hx-get="{{ request.path }}" 
                hx-target="#main-content"
                hx-include="[name=per_page], [name=locations_select], [name=schools_select]">
      </div>
      <div class="col-4">
        <label for="per_page" class="pl-2">Afficher</label>
        <select name="per_page" id="per_page" class="form-control form-control-sm" hx-get="{{ request.path }}?page={{ page }}"
                hx-target="#main-content" hx-include="[name=search], [name=locations_select], [name=schools_select]">
          <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 éléments</option>
          <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 éléments</option>
          <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 éléments</option>
          <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 éléments</option>
        </select> 
      </div>
    </div>
  
    {% if result_found %}
    <div class="alert alert-warning font-weight-bold d-flex justify-content-between border">
      <span><span data-feather="filter" class="feather-16 align-middle mr-2"></span> {{ result_found }} résultat(s)</span>
    </div>
    {% endif %}
  <div class="row" hx-get="{{ request.path }}" hx-trigger="saved from:body" hx-target="#main-content">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm d-table-sm table-actions table-striped table-bordered table-hover mb-0" data-table hx-boost="true">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white text-center">Photo</th>
                <th scope="col" class="text-white">Matricule ou ID</th>
                <th scope="col" class="text-white">Nom et Prénoms</th>
                <th scope="col" class="text-white">Né le</th>
                <th scope="col" class="text-white">Ecole d'origine</th>
                <th scope="col" class="text-white text-center">Ecole d'accueil</th>
                <th scope="col" class="text-white text-center">Statut</th>
                <th scope="col" class="text-white">Actions</th>
              </tr>
            </thead>
            <tbody id="tbody">
                {% for transfert in transferts %}
                <tr>
                  <td class="align-middle text-center" style="min-width: 60px;">
                    {% if transfert.enrollment.student.photo %}
                      <img data-original="{{ transfert.enrollment.student.photo.url }}" 
                          alt="1" 
                          class="lazy border img-thumbnail rounded-circle">
                    {% endif %}
                  </td>
                  <td class="align-middle"><a href="" 
                    hx-get=""
                    hx-target="#dialog">{% if transfert.enrollment.student.matricule %} {{ transfert.enrollment.student.matricule|default_if_none:'-' }} {% else %} {{ transfert.enrollment.student.identifier }} {% endif %}</a> {% if transfert.enrollment.student.full_name_ar %} {% endif %}</td>
                  <td class="align-middle">{{ transfert.enrollment.student }} </td>
                  <td class="align-middle">{{ transfert.enrollment.student.birth_date|date:'d/m/Y'|default_if_none:'' }}</td>
                  <td class="align-middle">{{ transfert.old_school }}</td>
                  <td class="align-middle">{{ transfert.school }}</td>
                  
                  <td class="align-middle text-center"><span class="badge {% if transfert.confirmed %} badge-success {% else %} badge-warning {% endif %}">
                    {% if transfert.confirmed %} VALIDE {% else %} EN ATTENTE {% endif %}
                  </span></td>
                  <td class="align-middle text-center">
                    <div class="dropdown">
                      <!-- {% if perms.exams.change_transfertrequest %}
                        <button class="btn btn-warning btn-sm" 
                                hx-get="" 
                                hx-target="#dialog"><i data-feather="edit"></i></button>
                      {% endif %} -->

                      {% if perms.exams.confirm_transfert %}
                        <button class="btn btn-info btn-sm" 
                                hx-get="{% url 'transfert_confirm' transfert.id %}" 
                                hx-target="#dialog"
                                {% if transfert.confirmed %} disabled="disabled" {% endif %}><i data-feather="check-square"></i>Valider</button>
                      {% endif %}

                    </div>
                  </td>
                </tr>
                {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items="[name=per_page], [name=search], [name=locations_select], [name=schools_select]" %}

        </div>
      </div>
    </div>
  </div>
</div>

<script>
$('img.lazy').lazyload({
  load: function() { $(this).removeClass("lazyload"); },
});

if (typeof(feather) !== 'undefined') {
feather.replace();
}

</script>
