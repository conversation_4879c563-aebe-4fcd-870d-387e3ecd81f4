{% load widget_tweaks %}
<form class="modal-content" id="modal-content" method="post" action="{% url 'school_center_cancel' object.id exam %}">
    {% csrf_token %}
    <div class="modal-header">
        <h5 class="modal-title">Confirmation d'annulation</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="alert alert-warning">
            <span data-feather="alert-triangle" class="feather-16 align-middle"></span>
            Vous êtes sur le point de retirer l'école <strong>{{ object.name|upper }}</strong> du centre <strong>{{ center.identifier|upper }}</strong> pour l'examen <strong>{{ exam|upper }}</strong>.
        </div>
        <p>Cette action supprimera l'association entre l'école et le centre pour l'année scolaire actuelle.</p>
        <p>Êtes-vous sûr de vouloir continuer?</p>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
        <button type="submit" class="btn btn-danger">
            <span data-feather="trash-2" class="feather-16 align-middle"></span> Retirer du centre
        </button>
    </div>
</form>

<script>
    if (typeof(feather) !== 'undefined') {
        feather.replace();
    }
</script>
