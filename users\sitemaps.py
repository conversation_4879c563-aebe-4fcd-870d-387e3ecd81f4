from django.contrib.sitemaps import Sitemap
from django.urls import reverse

class LoginSiteMapView(Sitemap):
    changefreq = "never"
    
    def items(self):
        return ['connexion']
    def location(self, item):
        return reverse('login')


class SpacesSiteMapView(Sitemap):
    changefreq = "never"
    
    def items(self):
        return ['espaces']
    def location(self, item):
        return reverse('espace_choice')