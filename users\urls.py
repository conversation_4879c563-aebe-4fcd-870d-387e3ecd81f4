from django.urls import path
from django.contrib.sitemaps.views import sitemap
from users.sitemaps import LoginSiteMapView, SpacesSiteMapView
from . import views

urlpatterns = [
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('espaces/', views.EspaceChoiceView.as_view(), name='espace_choice'),
    path('delegues/', views.CustomUserListView.as_view(), name='user_list'),
    path('delegues/ajouter/', views.CustomUserCreateView.as_view(), name='user_add'),
    path('delegues/<int:pk>/editer/', views.CustomUserUpdateView.as_view(), name='user_edit'),
]

sitemaps = {
    'connexion': LoginSiteMapView,
    'espaces': SpacesSiteMapView,
}
urlpatterns+= [
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps})
]