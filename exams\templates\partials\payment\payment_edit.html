{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">VERSEMENT</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Infos Versement</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.date.id_for_label }}">Date</label>
                {% render_field form.date class='form-control' %}
            </div>
            
            {% if updating %}
            <div class="form-group mb-2 col-md-6">
                <label for="id_location">Localité</label>
                <select name="location" hx-get="/centres_localite/" hx-target="#location_schools" class="form-control" required="" id="id_location">
                    {% for location in locations %}
                    <option value="{{ location.id }}" {% if location.id == location_id %} selected="select" {% endif %}>{{ location }}</option>
                    {% endfor %}
                </select>
            </div>
            {% else %}
                <div class="form-group mb-2 col-md-6">
                    <label for="{{ location_form.location.id_for_label }}">Localité</label>
                    {% render_field location_form.location class='form-control' hx-get='/centres_localite/' hx-target="#location_schools" %}
                </div>
            {% endif %}

            <div class="form-group mb-2 col-md-6" id="location_schools">
                <label for="{{ form.school.id_for_label }}">Nom de l'école *</label>
                 {% render_field form.school class='form-control text-uppercase' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.amount.id_for_label }}">Montant versé</label>
                {% render_field form.amount class='form-control' %}
            </div>
        </div>

    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>

<script>
    if (typeof(flatpickr) !== "undefined") {
        flatpickr("#id_date", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: true,
    });
}

</script>