{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Permissions</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      <strong class="text-muted">
      LISTE DES ACTIONS AUTORISEES PAR L'ADMINISTRATEUR  
      </strong>
    </h5>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-column justify-content-center align-items-center">
  {% if perms.exams.add_localcommission %}
      <div class="mb-3">
          <a class="btn btn-success text-white"
          hx-get="{% url 'permissions_edit' %}"
          hx-target="#dialog">
          <span data-feather="edit"></span>
           Modifier les actions autorisées </a>
      </div>
  {% endif %}

  <div class="container-fluid">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">ACTION</th>
                <th scope="col" class="text-white" style="min-width: 100px">STATUT</th>
              </tr>
            </thead>
            <tbody>
            <tr><td colspan="2" class="text-center"><strong>ACTIONS DES ECOLES</strong></td></tr>
            <tr>
                <td>Enregistrer des candidats</td>
                <td>
                    <span class="badge badge-pill {% if can_add_student %} badge-success {% else %} badge-danger {% endif %}">
                    {% if can_add_student %} OUI {% else %} NON {% endif %}
                    </span>
                </td>
            </tr>
            <tr>
                <td>Modifier des candidats</td>
                <td>
                    <span class="badge badge-pill {% if can_edit_student %} badge-success {% else %} badge-danger {% endif %}">
                    {% if can_edit_student %} OUI {% else %} NON {% endif %}
                    </span>
                </td>
            </tr>
            <tr><td colspan="2" class="text-center"><strong>ACTIONS DES COMMISSIONS LOCALES</strong></td></tr>
            <tr>
                <td>Valider des candidats</td>
                <td>
                    <span class="badge badge-pill {% if can_confirm_student %} badge-success {% else %} badge-danger {% endif %}">
                        {% if can_confirm_student %} OUI {% else %} NON {% endif %}
                    </span>
                </td>
            </tr>
            <tr>
                <td>Enregistrer des écoles</td>
                <td>
                    <span class="badge badge-pill {% if can_add_school %} badge-success {% else %} badge-danger {% endif %}">
                        {% if can_add_school %} OUI {% else %} NON {% endif %}
                    </span>
                    
                </td>
            </tr>
            <tr>
                <td>Modifier les infos des écoles</td>
                <td>
                    <span class="badge badge-pill {% if can_edit_school %} badge-success {% else %} badge-danger {% endif %}">
                    {% if can_edit_school %} OUI {% else %} NON {% endif %}
                    </span>
                </td>
            </tr>
            <tr>
                <td>Editer les notes pour l'examen National</td>
                <td>
                    <span class="badge badge-pill {% if can_edit_grade %} badge-success {% else %} badge-danger {% endif %}">
                    {% if can_edit_grade %} OUI {% else %} NON {% endif %}
                    </span>
                </td>
            </tr>
            <tr>
                <td>Editer les notes pour l'examen Blanc</td>
                <td>
                    <span class="badge badge-pill {% if can_edit_mock_grade %} badge-success {% else %} badge-danger {% endif %}">
                    {% if can_edit_mock_grade %} OUI {% else %} NON {% endif %}
                    </span>
                </td>
            </tr>
            <tr>
                <td>Demander des corrections</td>
                <td>
                    <span class="badge badge-pill {% if can_edit_correction %} badge-success {% else %} badge-danger {% endif %}">
                    {% if can_edit_correction %} OUI {% else %} NON {% endif %}
                    </span>
                </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
    if (typeof(feather) !== 'undefined') {
      feather.replace(); 
    }
</script>