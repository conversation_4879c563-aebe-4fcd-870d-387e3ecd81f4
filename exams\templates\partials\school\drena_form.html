{% load widget_tweaks %}

<form action="{% url 'send_drena_infos' %}" method="post" hx-post="{% url 'send_drena_infos' %}" hx-target="this" class="shadow p-4 rounded">
    {% csrf_token %} 
    <div class="alert alert-danger border-danger shadow">Veuillez définir votre DRENA et votre IEPP pour la production des cartes scolaires SVP !!</div>
    <div class="form-row">
        <div class="form-group mb-2 col-md-6">
            <label for="{{ form.drena.id_for_label }}">* DRENA</label>
            {% render_field form.drena class='form-control'%}
            <div class="invalid-feedback">{{ form.drena.errors|first }}</div>
        </div>
        <div class="form-group mb-2 col-md-6">
            <label for="{{ form.iepp.id_for_label }}">* IEPP</label>
             {% render_field form.iepp class='form-control text-uppercase' %}
             <div class="invalid-feedback">{{ form.iepp.errors|first }}</div>
        </div>
        <button type="submit" class="btn btn-success">Valider</button>
    </div>
</form>