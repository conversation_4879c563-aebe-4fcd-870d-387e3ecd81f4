from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from . import forms, models


@admin.register(models.CustomUser)
class CustomUserAdmin(UserAdmin):
    model = models.CustomUser
    form = forms.CustomUserChangeForm
    add_form = forms.CustomUserCreationForm
    school_fieldset = ((_("Informations sur l'école"), {'fields': ('school', 'role')}),)
    add_fieldsets = UserAdmin.add_fieldsets + school_fieldset
    fieldsets = UserAdmin.fieldsets + school_fieldset
    list_display = ['username', 'email', 'is_staff', 'school', 'role']
    list_filter = ['school']
    autocomplete_fields = ['school']