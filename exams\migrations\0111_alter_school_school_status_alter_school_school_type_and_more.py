# Generated by Django 4.2.1 on 2025-07-31 19:03

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0110_alter_schoolstatistics_options_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='school',
            name='school_status',
            field=models.CharField(choices=[('RC', "Réconnue par l'Etat"), ('NR', "Non réconnue par l'Etat")], max_length=2, null=True, verbose_name="Statut de l'école (Reconnu / Non rec.)"),
        ),
        migrations.AlterField(
            model_name='school',
            name='school_type',
            field=models.CharField(choices=[('ECC', 'Ecole Confessionnelle CHERIFLA'), ('ESIA', 'Etablissement Scolaire Islamique Affiliée')], max_length=4, null=True, verbose_name="Choisir le type d'école (ECC ou ESIA)"),
        ),
        migrations.Alter<PERSON>ield(
            model_name='school',
            name='teachers_count',
            field=models.PositiveSmallIntegerField(null=True, validators=[django.core.validators.MinValueValidator(1)], verbose_name="nombre d'enseignants"),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='boys_count',
            field=models.PositiveSmallIntegerField(null=True, validators=[django.core.validators.MinValueValidator(1)], verbose_name='nombre de garçons'),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='girls_count',
            field=models.PositiveSmallIntegerField(null=True, validators=[django.core.validators.MinValueValidator(1)], verbose_name='nombre de filles'),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='levels_count',
            field=models.PositiveSmallIntegerField(null=True, validators=[django.core.validators.MinValueValidator(1)], verbose_name='nombre de classes'),
        ),
        migrations.AlterField(
            model_name='schoolstatistics',
            name='students_count',
            field=models.PositiveSmallIntegerField(null=True, validators=[django.core.validators.MinValueValidator(1)], verbose_name="nombre total d'élèves"),
        ),
    ]
