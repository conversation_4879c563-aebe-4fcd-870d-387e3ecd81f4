{% load humanize %}
<div class="adminx-main-content">
    <div class="container-fluid">
      <!-- BreadCrumb -->
      <nav aria-label="breadcrumb" role="navigation">
        <ol class="breadcrumb adminx-page-breadcrumb">
          <li class="breadcrumb-item"><a href="{% url 'home' %}">Accueil</a></li>
          <li class="breadcrumb-item active" aria-current="page">Tableau de bord</li>
        </ol>
      </nav>

      {% if user.is_authenticated %}
<!--       
      <div class="container-fluid my-3" id="info-alert">
        <div class="alert alert-success border border shadow">
          <span data-feather="info" class="align-middle feather-16 feather-success"></span>
          Les écoles peuvent maintenant faire les demandes de cartes scolaires pour les classes intermédiaires dans la rubrique Carte Scolaire.
        </div>
      </div>
       -->
      {% if perms.exams.view_results %}
      <div class="pb-3 text-muted">
        {% if user.role == ROLE_COMMISSION_LOCALE %}:
          <h4 class="font-weight-bold"><i data-feather="users"></i> Résultat Général de la Localité</h4>
        {% else %}
        <h4><i data-feather="users"></i> <strong>Résultat National</strong></h4>
        {% endif %}
      </div>

      <div class="row">
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-primary">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-primary border">
                <span>CEPE</span>
              </div>
              <div class="card-body">
                <h5><strong>CANDIDATS PRESENTS</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{{ cepe_statistics.present|stringformat:'02d' }} SUR {{ cepe_statistics.candidates|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-primary">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-primary border">
                <span>CEPE</span>
              </div>
              <div class="card-body">
                <h5><strong>CANDIDATS ADMIS</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{{ cepe_statistics.admitted|stringformat:'02d' }} SUR {{ cepe_statistics.present|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-primary">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center border">
                <span>CEPE</span>
              </div>
              <div class="card-body">
                <h5><strong>POURCENTAGE</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{{ cepe_statistics.perc|floatformat:2 }} %</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-warning">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-warning border">
                <span>BEPC</span>
              </div>
              <div class="card-body">
                <h5><strong>CANDIDATS PRESENTS</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{{ bepc_statistics.present|stringformat:'02d' }} SUR {{ bepc_statistics.candidates|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-warning">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-warning border">
                <span>BEPC</span>
              </div>
              <div class="card-body">
                <h5><strong>CANDIDATS ADMIS</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{{ bepc_statistics.admitted|stringformat:'02d' }} SUR {{ bepc_statistics.present|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-warning">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center border">
                <span>BEPC</span>
              </div>
              <div class="card-body">
                <h5><strong>POURCENTAGE</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{{ bepc_statistics.perc|floatformat:2 }} %</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-success">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-success border">
                <span>BAC</span>
              </div>
              <div class="card-body">
                <h5><strong>CANDIDATS PRESENTS</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{% if bac_statistics.candidates > 0 %} {{ bac_statistics.present|stringformat:'02d' }} SUR {{ bac_statistics.candidates|stringformat:'02d' }} {% else %} AUCUN CANDIDAT {% endif %}</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-success">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-success border">
                <span>BAC</span>
              </div>
              <div class="card-body">
                <h5><strong>CANDIDATS ADMIS</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{% if bac_statistics.candidates > 0 %} {{ bac_statistics.admitted|stringformat:'02d' }} SUR {{ bac_statistics.present|stringformat:'02d' }} {% else %} AUCUN CANDIDAT {% endif %}</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-success">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center border">
                <span>BAC</span>
              </div>
              <div class="card-body">
                <h5><strong>POURCENTAGE</strong></h5>
                <h5 class="card-title mb-0 font-weight-bold">{% if bac_statistics.perc %} {{ bac_statistics.perc|floatformat:2 }} {% else %} 0 {% endif %} %</h5>
              </div>
            </div>
          </div>
        </div>
      
        {% elif user.role != 'DG' %}
      <!-- {% if user.role == ROLE_COMMISSION_LOCALE and has_invalid_candidates %}
      <div class="mx-2">
        <div class="alert alert-warning" role="alert">
          <span data-feather="alert-triangle"></span>
          Rappel: Vous avez des candidats non validés. N'oubliez pas de les
          valider avant la clôture des inscriptions.
        </div>
      </div>
      {% endif %} -->

      <!-- {% if user.role != ROLE_ECOLE %}
      <div class="pb-3 mt-4 text-danger">
        <h4><i data-feather="circle"></i> <strong>Examens {{ active_year }}</strong></h4>
      </div>

      <div class="row">
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-2 text-center mb-grid w-100 shadow-sm rounded alert-danger">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-danger border">
                <span>CENTRES</span>
              </div>
              <div class="card-body">
                <h5><strong>{{ CENTER_TRANSLATION }}</strong></h5>
                <h5 class="card-title mb-0 fw-bold">{{ centers_count|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>
      </div>
      {% endif %} -->

      {% if infos_needed and user.role == ROLE_ECOLE  %}
          {% include 'partials/school/drena_form.html' %}
      {% endif %}

      <div class="pb-0 mt-4 text-primary">
        <h4><i data-feather="users"></i> Elèves Inscrits</h4>
      </div>

      <div class="row">
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-warning">
            <div class="d-flex flex-row align-items-start h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center alert alert-warning border">
                <span>CEPE</span>
              </div>
              <div class="card-body">
                <h5><strong>{{ CEPE_TRANSLATION }}</strong></h5>
                <h5 class="card-title mb-0 fw-bold">{{ cepe_students_count|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-warning">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center border">
                <span>BEPC</span>
              </div>
              <div class="card-body">
                <h5><strong>{{ BEPC_TRANSLATION }}</strong></h5>
                <h5 class="card-title mb-0 fw-bold">{{ bepc_students_count|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-1 text-center mb-grid w-100 shadow-sm rounded alert-warning">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center border">
                <span>BAC</span>
              </div>
              <div class="card-body">
                <h5><strong>{{ BAC_TRANSLATION }}</strong></h5>
                <h5 class="card-title mb-0 fw-bold">{{ bac_students_count|stringformat:'02d' }}</h5>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pb-0 text-success" >
        <h4> <i data-feather="check-square"></i>Candidats validés</h4>
      </div>

      <div class="row">
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-0 bg-success text-white text-center mb-grid w-100 rounded">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center">
                <span>CEPE</span>
              </div>
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5><strong>Effectif</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cepe_candidates|stringformat:'02d' }}</h5>
                </div>
                <div>
                  <h5><strong>Droits d'Examen</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cepe_candidates_fees|intcomma }} F</h5>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-0 bg-success text-white text-center mb-grid w-100 rounded">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center">
                <span>BEPC</span>
              </div>
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5><strong>Effectif</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ bepc_candidates|stringformat:'02d' }}</h5>
                </div>
                <div>
                  <h5><strong>Droits d'Examen</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ bepc_candidates_fees|intcomma }} F</h5>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-0 bg-success text-white text-center mb-grid w-100 rounded">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center">
                <span>BAC</span>
              </div>
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5><strong>Effectif</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ bac_candidates|stringformat:'02d' }}</h5>
                </div>
                <div>
                  <h5><strong>Droits d'Examen</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ bac_candidates_fees|intcomma }} F</h5>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pb-0 text-info">
        <h4 style="color: chocolate;"><i data-feather="credit-card"></i> Cartes Scolaires</h4>
      </div>

      <div class="row">
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-0 bg-info text-white text-center mb-grid w-100 rounded">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center">
                <span>CEPE</span>
              </div>
              <div class="card-body d-flex justify-content-between">
                <div class="">
                  <h5><strong>Demandes</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.cepe_cards|stringformat:'02d' }}</h5>
                </div>
                <div class="">
                  <h5><strong>Produites</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.cepe_manufactured|stringformat:'02d' }}</h5>
                </div>
                <div class="">
                  <h5><strong>Livrées</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.cepe_shipped|stringformat:'02d' }}</h5>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-0 bg-info text-white text-center mb-grid w-100 rounded">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center">
                <span>BEPC</span>
              </div>
              <div class="card-body d-flex justify-content-between">
                <div class="">
                  <h5><strong>Demandes</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.bepc_cards|stringformat:'02d' }}</h5>
                </div>
                <div class="">
                  <h5><strong>Produites</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.bepc_manufactured|stringformat:'02d' }}</h5>
                </div>
                <div class="">
                  <h5><strong>Livrées</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.bepc_shipped|stringformat:'02d' }}</h5>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4 d-flex">
          <div class="card border-0 bg-info text-white text-center mb-grid w-100 rounded">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center">
                <span>CLASSES <br> INTERME- <br> DIAIRES</span>
              </div>
              <div class="card-body d-flex justify-content-between">
                <div class="">
                  <h5><strong>Demandes</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.other_cards|stringformat:'02d' }}</h5>
                </div>
                <div class="">
                  <h5><strong>Produites</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.other_manufactured|stringformat:'02d' }}</h5>
                </div>
                <div class="">
                  <h5><strong>Livrées</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ cards.other_shipped|stringformat:'02d' }}</h5>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {% else %}
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
      <div class="row">
        <div class="col-md-6 col-lg-4 d-flex" style="max-height: 100px;">
          <div class="card border-0 alert-warning text-white text-center mb-grid w-100 rounded">
            <div class="d-flex flex-row align-items-center h-100">
              <div class="card-icon d-flex align-items-center h-100 justify-content-center">
                <span>EFFECTIFS</span>
              </div>
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5><strong>Garçons</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ stats.hr_male|stringformat:'02d' }}</h5>
                </div>
                <div>
                  <h5><strong>Filles</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ stats.hr_female|stringformat:'02d' }}</h5>
                </div>
                <div>
                  <h5><strong>Total</strong></h5>
                  <h5 class="card-title mb-0 fw-bold">{{ stats.hr_total|stringformat:'02d' }}</h5>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card border-0 shadow-sm">
            <div class="card-body">
              <h5 class="card-title text-center mb-4">État des Cartes</h5>
              <canvas id="cardsChart"></canvas>
            </div>
          </div>
        </div>
      </div>

      <script>
      if (typeof(Chart) !== 'undefined') {
        // Helper function for percentage calculation
        const calculatePercentage = (value, total) => ((value / total) * 100).toFixed(1);

        // Cards Status Chart
        const cardsTotal = {{ stats.hr_pending }} + {{ stats.hr_manufactured }} + {{ stats.hr_shipped }};
        new Chart(document.getElementById('cardsChart'), {
          type: 'pie',
          data: {
            labels: ['En cours', 'Produites', 'Livrées'],
            datasets: [{
              data: [
                {{ stats.hr_pending }},
                {{ stats.hr_manufactured }},
                {{ stats.hr_shipped }}
              ],
              backgroundColor: [
                '#FFC107',
                '#17A2B8', 
                '#28A745'
              ]
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                position: 'bottom'
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const value = context.raw;
                    const percentage = calculatePercentage(value, cardsTotal);
                    return `${context.label}: ${value}`;
                  }
                }
              },
              datalabels: {
                color: '#fff',
                font: {
                  weight: 'bold',
                  size: 18,
                },
                formatter: function(value) {
                  return `${value}`;
                }
              }
            }
          },
          plugins: [ChartDataLabels]
        });
      }
      </script>
            {% endif %}
            {% endif %}
    </div>
  </div>