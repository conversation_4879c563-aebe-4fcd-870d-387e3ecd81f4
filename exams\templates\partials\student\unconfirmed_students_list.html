{% load humanize %}
<div class="container-fluid mt-2">
  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0" data-table id="table">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">Identifiant</th>
                <th scope="col" class="text-white">Nom et Prénoms</th>
                <th scope="col" class="text-white">Né(e) le</th>
                <th scope="col" class="text-white">Dossier</th>
              </tr>
            </thead>
            <tbody>
              {% for enrollment in enrollments %}
              <tr>
                <td><span>{{ enrollment.student.identifier }}</span></td>
                <td>{{ enrollment.student }} <br> 
                    {{ enrollment.student.full_name_ar|default_if_none:'' }} <br>  </td>
                <td>{{ enrollment.student.birth_date|date:'d/m/Y' }}</td>
                <td><span class="badge {% if enrollment.confirmed %} badge-success {% else %} badge-warning {% endif %}">
                  {% if enrollment.confirmed %} VALIDE {% else %} OUVERT {% endif %}
                </span></td>

              </tr>
              {% endfor %}
              
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <ul class="list-group">
    <li class="list-group-item d-flex justify-content-between bg-warning align-items-center pr-3">
      Résumé des dossiers non validés
    </li>
    <li class="list-group-item d-flex justify-content-between align-items-center pr-3">
      Dossiers à valider
      <span class="badge badge-primary badge-pill badge-warning">{{ unconfirmed_candidates }} candidat{{ unconfirmed_candidates|pluralize }}</span>
    </li>
    <li class="list-group-item d-flex justify-content-between align-items-center pr-3">
      Montant à verser
      <span class="badge badge-primary badge-pill badge-warning">{{ exam_fees|intcomma }} F CFA</span>
    </li>
  </ul>
</div>
<script>
  feather.replace();
</script>
