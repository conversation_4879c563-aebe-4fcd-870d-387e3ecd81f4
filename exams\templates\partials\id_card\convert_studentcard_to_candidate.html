{% extends 'components/modal.html' %}
{% load widget_tweaks %}

{% block modal_title %}Convertir une demande de carte scolaire en Candidature CHERIFLA{% endblock %}

{% block modal_body %}
    <div class="modal-body">
        <span>Vous êtes sur le point de <span class="fw-bold text-danger">CONVERTIR</span>
        l'objet DEMANDE DE CARTE SCOLAIRE de:</span> 
        <span class="fw-bold"><strong>{{ studentcard }}</strong></span> <br>
        Né(e) le {{ studentcard.birth_date|date:'d F Y'|default_if_none:'' }} à 
        {{ studentcard.birth_place|default_if_none:'' }} <br>
        en classe de <strong>{{ studentcard.get_level_display }};</strong>
        <br>EN CANDIDATURE <br>
        <div class="row">
            <div class="form-group mb-2 col-md-12 mb-3">
                <label for="{{ form.id_exam }}" class="font-weight-bold">à l'examen du</label>
                {% render_field form.exam class='form-control' %}
            </div>
        </div>
        <br>
        CONFIRMER?
    </div>
{% endblock %}

{% block modal_footer %}
    <button type="submit" class="btn btn-success">Confirmer</button>
{% endblock %}

<script>
    hideSubmitButtonIfFormValid()
</script>