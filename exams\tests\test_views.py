import datetime
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission, Group
from django.core import exceptions
from django.test import TestCase
from django.urls import reverse
from model_bakery import baker
from .. import models, views, forms
from project_utils import constants, custom_utils

htmx_headers = {'HTTP_HX_Request':'true'}

class TestHomePageView(TestCase):
    def setUp(self):
        self.user_local = baker.make(
            get_user_model(), role=constants.ROLE_COMMISSION_LOCALE)
        self.local_commission = baker.make(models.LocalCommission, 
            user=self.user_local)
        self.other_local_commission = baker.make(models.LocalCommission)
        self.location_school1 = baker.make(models.School, 
            local_commission=self.local_commission)
        self.location_school2 = baker.make(models.School,
            local_commission=self.local_commission)
        other_school = baker.make(models.School, 
            local_commission=self.other_local_commission)
        
        self.user_ecole = baker.make(
            get_user_model(), role=constants.ROLE_ECOLE, 
            school=self.location_school1) 
        self.other_user_ecole = baker.make(
            get_user_model(), role=constants.ROLE_ECOLE, 
            school=self.location_school2) 
        
        self.other_user= baker.make(
            get_user_model(), role=constants.ROLE_ECOLE, 
            school=other_school) 
        self.user_national = baker.make(
            get_user_model(), role=constants.ROLE_COMMISSION_NATIONALE)
        
        self.active_year = baker.make(models.Year, is_current=True, short_name=2023)
        school1_unconfirmed_enrollments = baker.make(
            models.Enrollment, 2, exam=constants.EXAM_CEPE, 
            year=self.active_year, school=self.location_school1, 
            agent=self.user_ecole, confirmed=False, active=True)
        school1_confirmed_enrollments = baker.make(
            models.Enrollment, 2, exam=constants.EXAM_CEPE, 
            year=self.active_year, school=self.location_school1, 
            agent=self.user_ecole, confirmed=True, active=True)
        school2_enrollments = baker.make(
            models.Enrollment, 2, exam=constants.EXAM_BEPC, 
            year=self.active_year, school=self.location_school2, 
            agent=self.other_user_ecole, active=True)
        other_enrollments = baker.make(
            models.Enrollment, 2, exam=constants.EXAM_BEPC, 
            year=self.active_year, school=other_school, 
            agent=self.other_user_ecole, active=True)

    def test_anonymous_requests(self):
        resp = self.client.get(reverse('home'))
        self.assertEqual(resp.status_code, 302)
        self.assertRedirects(resp, reverse('espace_choice'))
    
    def test_authenticated_requests(self):
        self.client.force_login(self.user_ecole)
        resp = self.client.get(reverse('home'))
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'index.html')

    def test_context_for_user_ecole(self):
        self.client.force_login(self.user_ecole)
        resp = self.client.get(reverse('home'))

        # User ecole context
        self.assertEqual(resp.context['cepe_students_count'], 4)
        self.assertEqual(resp.context['bepc_students_count'], 0)
        self.assertEqual(resp.context['cepe_candidates'], 2)
        self.assertEqual(resp.context['bepc_candidates'], 0)
        self.assertEqual(resp.context['bac_candidates'], 0)

        # Other user ecole
        self.client.force_login(self.other_user_ecole)
        resp = self.client.get(reverse('home'))
        self.assertEqual(resp.context['cepe_students_count'], 0)
        self.assertEqual(resp.context['bepc_students_count'], 2)
        self.assertEqual(resp.context['cepe_candidates'], 0)
        self.assertEqual(resp.context['bepc_candidates'], 0)
        self.assertEqual(resp.context['bac_candidates'], 0)

    def test_context_for_user_local(self):
        self.client.force_login(self.user_local)
        resp = self.client.get(reverse('home'))
        self.assertEqual(resp.context['cepe_students_count'], 4)
        self.assertEqual(resp.context['bepc_students_count'], 2)
        self.assertEqual(resp.context['cepe_candidates'], 2)
        self.assertEqual(resp.context['bepc_candidates'], 0)
        self.assertEqual(resp.context['bac_candidates'], 0)
    
    def test_context_for_user_national(self):
        self.client.force_login(self.user_national)
        resp = self.client.get(reverse('home'))
        self.assertEqual(resp.context['cepe_students_count'], 4)
        self.assertEqual(resp.context['bepc_students_count'], 4)
        self.assertEqual(resp.context['cepe_candidates'], 2)
        self.assertEqual(resp.context['bepc_candidates'], 0)
        self.assertEqual(resp.context['bac_candidates'], 0)


class CandidateListViewTests(TestCase):
    def setUp(self):
        self.local_commission = baker.make(models.LocalCommission)
        self.year = baker.make(models.Year, is_current=True, short_name=2023)
        self.school = baker.make(models.School, local_commission=self.local_commission)
        self.user = baker.make(get_user_model(), school=self.school)
        enrollments = baker.make(models.Enrollment, 4, 
            active=True, school=self.school, agent=self.user, 
            year=self.year)
        self.url = reverse('candidates', 
                           args=[self.year.short_name, constants.EXAM_CEPE])

    def test_anonymous_requests(self):
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, f"{reverse('login')}?next={self.url}")

    def test_authenticated_requests(self):
        self.client.force_login(self.user)

        # Non-htmx requests
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'full_template.html')

        # Htmx requests
        resp = self.client.get(self.url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/students_list.html')
        self.assertTemplateNotUsed(resp, 'full_template.html')

    def test_context_data(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertIn('selected_year', resp.context)
        self.assertIn('exam', resp.context)
        self.assertEqual(resp.context['exam'], constants.EXAM_CEPE.upper())
        self.assertEqual(resp.context['selected_year'], self.year.short_name)


class CandidateDetailViewTests(TestCase):
    def setUp(self):
        self.local_commission = baker.make(models.LocalCommission)
        self.year = baker.make(models.Year, is_current=True, short_name=2023)
        self.school = baker.make(models.School, 
            local_commission=self.local_commission)
        self.user = baker.make(get_user_model(), school=self.school)
        enrollments = baker.make(models.Enrollment, 4, 
            active=True, school=self.school, agent=self.user, 
            year=self.year)
        self.url = reverse('candidate_detail', 
            args=[self.year.short_name, enrollments[0].id])
        self.user.user_permissions.add(
            Permission.objects.get(codename='view_student')
        )
        
    def test_non_htmx_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 302)
    
    def test_htmx_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/student_detail.html')


class CandidateEditViewTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, is_current=True, short_name=2023)
        self.school = baker.make(models.School)
        self.user = baker.make(get_user_model() , school=self.school)
        self.url = reverse('candidate_add', args=[constants.EXAM_CEPE])
        self.candidates_url = reverse('candidates', 
            args=[self.year.short_name, constants.EXAM_CEPE])

    def test_authorized_non_htmx_get_requests(self):
        self.user.user_permissions.add(
            Permission.objects.get(codename='change_student')
        )
        self.client.force_login(self.user)
        resp = self.client.get(
            reverse('candidate_add', args=[constants.EXAM_CEPE]))
        self.assertRedirects(resp, self.candidates_url)

    def test_authorized_htmx_get_requests(self):
        self.user.user_permissions.add(
            Permission.objects.get(codename='change_student')
        )

        self.client.force_login(self.user)
        resp = self.client.get(
            reverse('candidate_add', args=[constants.EXAM_CEPE]), 
            **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/student_edit.html')


class CandidateStatusChangeViewTests(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, is_current=True, short_name=2023)
        self.local_user = baker.make(get_user_model() , 
            role=constants.ROLE_COMMISSION_LOCALE)
        self.local_commission = baker.make(models.LocalCommission, user=self.local_user)
        self.school = baker.make(models.School, local_commission=self.local_commission)
        self.ecole_user = baker.make(get_user_model() , 
            school=self.school, role=constants.ROLE_ECOLE)
        
        self.national_user = baker.make(get_user_model() , 
            role=constants.ROLE_COMMISSION_NATIONALE)
        self.enrollment = baker.make(
            models.Enrollment, school=self.school, 
            agent=self.local_user, year=self.year, exam=constants.EXAM_CEPE)
        
        self.other_local_user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_LOCALE)
        self.other_commission = baker.make(models.LocalCommission, user=self.other_local_user)
        self.other_school = baker.make(models.School, 
            local_commission=self.other_commission)
        self.other_ecole_user = baker.make(get_user_model(), 
            school=self.other_school)
        
    def test_unauthorized_requests(self):
        self.client.force_login(self.ecole_user)
        resp = self.client.get(
            reverse('confirm_status', 
                    args=[constants.EXAM_CEPE, self.enrollment.id]
            ), **htmx_headers
        )
        self.assertEqual(resp.status_code, 302)
        self.assertRedirects(resp, reverse('home'))

    def test_status_change_for_ecole_and_national_users(self):
        self.client.force_login(self.ecole_user)
        url = reverse('confirm_status', 
                      args=[constants.EXAM_CEPE, self.enrollment.id])
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 302)
        self.assertRedirects(resp, reverse('home'))

        self.client.logout()
        self.client.force_login(self.national_user)
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 302)
        self.assertRedirects(resp, reverse('home'))

    def test_status_change_for_commission_local_users(self):
        # A commission local user from one location
        # must not be able to confirm students from another one
        url = reverse('confirm_status', 
                      args=[constants.EXAM_CEPE, self.enrollment.id])
        self.other_local_user.user_permissions.add(
            Permission.objects.get(codename='change_student_status')
        )
        self.client.force_login(self.other_local_user)
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 302)
        self.assertRedirects(resp, reverse('home'))

        # Commission Local user within same location as ecole user
        # Should be able to confirm students in the same location
        self.client.logout()
        self.local_user.user_permissions.add(
            Permission.objects.get(codename='change_student_status')
        )
        self.client.force_login(self.local_user)
        resp = self.client.get(url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/student_status_change_confirm.html')


class CandidatesBulkStatusChangeView(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, is_current=True, short_name=2023)
        self.local_user = baker.make(get_user_model() , 
            role=constants.ROLE_COMMISSION_LOCALE)
        self.local_commission = baker.make(models.LocalCommission, user=self.local_user)
        self.school = baker.make(models.School, local_commission=self.local_commission)
        self.ecole_user = baker.make(get_user_model() , 
            school=self.school, role=constants.ROLE_ECOLE)
        self.local_user.user_permissions.add(
            Permission.objects.get(codename='change_student_status')
        )
        self.enrollments = baker.make(
            models.Enrollment, 3, school=self.school, 
            agent=self.local_user, year=self.year, exam=constants.EXAM_CEPE, 
            confirmed=False)
        self.enrollments = baker.make(
            models.Enrollment, 6, school=self.school, 
            agent=self.local_user, year=self.year, exam=constants.EXAM_BEPC, 
            confirmed=False)
        self.url = reverse('confirm_school_candidates', args=[constants.EXAM_CEPE])

    def test_unauthorized_requests(self):
        self.client.force_login(self.ecole_user)
        resp = self.client.get(self.url, **htmx_headers)      
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, f"{reverse('login')}?next={self.url}")
    
    def test_authorized_requests(self):
        self.client.force_login(self.local_user)
        resp = self.client.get(self.url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/student_status_change_confirm_bulk.html')

    def test_post_request(self):
        self.client.force_login(self.local_user)
        data = {'exam': constants.EXAM_CEPE, 'school': self.school.id}
        resp = self.client.post( 
            self.url, data=data, **htmx_headers)
        self.assertEqual(resp.status_code, 302)

        url = reverse('candidates', args=[self.year.short_name, constants.EXAM_CEPE])
        self.assertRedirects(resp, url)
        qs = models.Enrollment.candidates.get_candidates(
            year=self.year, user=self.ecole_user, exam=constants.EXAM_CEPE)
        self.assertTrue(qs.first().confirmed)


class UnconfirmedCandidatesListView(TestCase):
    def setUp(self):
        self.year = baker.make(models.Year, is_current=True, short_name=2023)
        self.local_commission = baker.make(models.LocalCommission)
        self.school = baker.make(models.School, local_commission=self.local_commission)
        self.ecole_user = baker.make(get_user_model() , 
            school=self.school, role=constants.ROLE_ECOLE)
        self.enrollments = baker.make(
            models.Enrollment, 2, school=self.school, 
            agent=self.ecole_user, year=self.year, exam=constants.EXAM_CEPE, 
            confirmed=False)
        self.enrollments = baker.make(
            models.Enrollment, 3, school=self.school, 
            agent=self.ecole_user, year=self.year, exam=constants.EXAM_CEPE, 
            confirmed=True)
        
    def test_list_page(self):
        self.client.force_login(self.ecole_user)
        url = reverse('unconfirmed_candidates')
        data = {'exam': constants.EXAM_CEPE, 'school': self.school.id}
        resp = self.client.get(url, data=data, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/student/unconfirmed_students_list.html')


class CandidateDeleteViewTests(TestCase):
    def setUp(self):
        self.local_commission = baker.make(models.LocalCommission)
        self.year = baker.make(models.Year, is_current=True, short_name=2023)
        self.school = baker.make(models.School, 
            local_commission=self.local_commission)
        self.user = baker.make(get_user_model(), 
            school=self.school, role=constants.ROLE_ECOLE)
        self.confirmed = baker.make(models.Enrollment, 
            active=True, school=self.school, agent=self.user, 
            year=self.year, confirmed=True)
        self.unconfirmed = baker.make(models.Enrollment, 
            active=True, school=self.school, agent=self.user, 
            year=self.year, confirmed=False)
        self.delete_confirmed_url = reverse('candidate_delete', 
                           args=[self.confirmed.id])
        self.delete_unconfirmed_url = reverse('candidate_delete', 
                           args=[self.unconfirmed.id])

    def test_anonymous_requests(self):
        resp = self.client.get(self.delete_confirmed_url)
        self.assertEqual(resp.status_code, 302)

    def test_authenticated_requests(self):
        perm, created = Permission.objects.get_or_create(codename='delete_student')
        self.user.user_permissions.add(perm)
        self.client.force_login(self.user)

        obj = models.Enrollment.candidates.unconfirmed(
            year=self.year, user=self.user, confirmed=False
        ).first()
        resp = self.client.post(reverse('candidate_delete', args=[obj.id]))
        queryset  = models.Enrollment.candidates.unconfirmed(
            year=self.year, user=self.user, confirmed=False
        )
        self.assertNotIn(obj, queryset)
        self.assertRedirects(resp, reverse('candidates', 
            args=[self.year.short_name, self.unconfirmed.exam]))
        
        # Should not delete
        resp = self.client.post(self.delete_confirmed_url)
        self.assertEqual(resp.status_code, 404)


class LocalCommissionListViewTestCase(TestCase):
    def setUp(self):
        year = baker.make(models.Year, is_current=True, short_name=2023)
        active_commissions = baker.make(models.LocalCommission, 2)
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_NATIONALE)
        perm, created = Permission.objects.get_or_create(
            codename='view_localcommission')
        self.user.user_permissions.add(perm)
        baker.make(models.LocalCommission, user=self.user)

    def test_unauthorized_users(self):
        user_ecole = baker.make(get_user_model(), role=constants.ROLE_ECOLE)
        self.client.force_login(user_ecole)
        resp = self.client.get(reverse('commissions'))
        self.assertRedirects(resp, reverse('home'))
    
    def test_authorized_non_htmx_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(reverse('commissions'))
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'full_template.html')

    def test_authorized_htmx_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(reverse('commissions'), **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 
            'partials/commissions/commissions_list.html')
        self.assertTemplateNotUsed(resp, 'full_template.html')

    def test_context_object(self):
        self.client.force_login(self.user)
        resp = self.client.get(reverse('commissions'))
        self.assertEqual(resp.status_code, 200)
        self.assertIn('schools_count', resp.context)
        self.assertIn('ROLE_COMMISSION_NATIONALE', resp.context)
        self.assertIn('commissions', resp.context)


class CommissionsEditViewTests(TestCase):
    def setUp(self):
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_NATIONALE)
        perm, created = Permission.objects.get_or_create(
            codename='add_localcommission')
        self.user.user_permissions.add(perm)
        
    def test_unauthorized_requests(self):
        local_user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_LOCALE)
        local_commission = baker.make(models.LocalCommission, 
            user=local_user)
        self.client.force_login(local_user)
        resp = self.client.get(reverse('commission_add'))
        url = f"{reverse('login')}?next={reverse('commission_add')}"
        self.assertEqual(resp.url, url)

    def test_getting_add_with_no_htmx_headers(self):
        self.client.force_login(self.user)
        resp = self.client.get(reverse('commission_add'))
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, reverse('commissions'))

    def test_creating_a_commission(self):
        self.client.force_login(self.user)
        resp = self.client.get(reverse('commission_add'), **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 
            'partials/commissions/commission_edit.html')
        
        resp = self.client.post(reverse('commission_add'), {
            'location': 'DUEKOUE',
            'director': 'SABA ABDOULAYE',
            'phone': '0545845598   ',
        })
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, reverse('commissions'))
        
        # Ensure the object was created successfully
        commissions = models.LocalCommission.objects.all()
        self.assertEqual(commissions.count(), 1)

        password = str(commissions.first().phone).strip()
        self.assertEqual(commissions.first().initial_password, password)
        
        # Ensure a new user was created for the commission
        username = commissions.first().location.upper()
        user = get_user_model().objects.filter(username=username).first()
        self.assertIsNotNone(user)
        self.assertEqual(user.localcommission, commissions.first())

    def test_updating_a_commission(self):
        self.client.force_login(self.user)
        resp = self.client.post(reverse('commission_add'), {
            'location': 'DUEKOUE',
            'director': 'SABA ABDOULAYE',
            'phone': '    0545845598   ',
        })
        
        commission = models.LocalCommission.objects.first()
        updated_phone = '0555555555   '
        resp = self.client.post(
            reverse('commission_edit', args=[commission.id]), 
            data={
            'phone': updated_phone, 
            'location': commission.location, 
            'director': commission.get_full_name() })
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, reverse('commissions'))

        commission = models.LocalCommission.objects.first()
        self.assertEqual(commission.phone, updated_phone.strip())


class SchoolListViewTests(TestCase):
    def setUp(self):
        self.school = baker.make(
            models.School,
            name='Test School',
            director='Test Director',
            phone='1234567890'
        )
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_LOCALE)
        self.commission = baker.make(models.LocalCommission, user=self.user)
        self.list_url = reverse('schools')

    def test_unauthorized_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.list_url)
        self.assertRedirects(resp, reverse('home'))

    def test_authorized_requests(self):
        perm, created = Permission.objects.get_or_create(codename='view_school')
        self.user.user_permissions.add(perm)
        self.client.force_login(self.user)
        resp = self.client.get(self.list_url)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'full_template.html')

        # With Htmx
        resp = self.client.get(self.list_url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/school/schools_list.html')
        self.assertTemplateNotUsed(resp, 'full_template.html')
    
    def test_context_object(self):
        perm, created = Permission.objects.get_or_create(codename='view_school')
        self.user.user_permissions.add(perm)
        self.client.force_login(self.user)
        resp = self.client.get(self.list_url)
        self.assertIn('schools_count', resp.context)
        self.assertIn('ROLE_COMMISSION_LOCALE', resp.context)
        self.assertIn('ROLE_COMMISSION_NATIONALE', resp.context)
        self.assertEqual(resp.context['section'], 'schools')

class SchoolCreateAndUpdateViewTests(TestCase):
    def setUp(self):
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_LOCALE)
        self.commission = baker.make(models.LocalCommission, user=self.user)
        self.list_url = reverse('schools')
        self.add_url = reverse('school_add')

    def test_unauthorized_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.add_url)
        self.assertRedirects(resp, reverse('home'))

    def test_authorized_requests(self):
        perm, created = Permission.objects.get_or_create(codename='add_school')
        self.user.user_permissions.add(perm)
        self.client.force_login(self.user)

        # Non-Htmx requests should redirect to schools page
        resp = self.client.get(self.add_url)
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, self.list_url)

        # Htmx requests should work and return the form
        resp = self.client.get(self.add_url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/school/school_edit.html')
        self.assertIsInstance(resp.context['form'], forms.ModelForm)

    def test_context_object(self):
        perm, created = Permission.objects.get_or_create(codename='add_school')
        self.user.user_permissions.add(perm)
        self.client.force_login(self.user)
        resp = self.client.get(self.add_url, **htmx_headers)
        self.assertIn('ROLE_COMMISSION_NATIONALE', resp.context)


class SchoolUpdateViewTests(TestCase):
    def setUp(self):
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_LOCALE)
        self.commission = baker.make(models.LocalCommission, user=self.user)
        school = baker.make(models.School, confirmed=False, 
            local_commission=self.commission)
        self.list_url = reverse('schools')
        self.edit_url = reverse('school_edit', args=[school.id])

    def test_unauthorized_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.edit_url)
        self.assertRedirects(resp, reverse('home'))

    def test_authorized_requests(self):
        perm, created = Permission.objects.get_or_create(codename='change_school')
        self.user.user_permissions.add(perm)
        self.client.force_login(self.user)

        # Non-Htmx requests should redirect to schools page
        resp = self.client.get(self.edit_url)
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, self.list_url)

        # Htmx requests should work and return the form
        resp = self.client.get(self.edit_url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/school/school_edit.html')
        self.assertIsInstance(resp.context['form'], forms.ModelForm)
        
        # Update
        data = {
            'school_type': 'ECC',
            'school_cycle': 'P',
            'school_status': 'RC',
            'teachers_count': 10,
            'name': 'NewName',
            'local_commission': self.commission.id,
            'director': 'NyDirector',
            'phone': '0545845598',
        }
        resp = self.client.post(
            self.edit_url, data=data, **htmx_headers, follow=True)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'components/main.html')


class SchoolConfirmViewTests(TestCase):
    def setUp(self):
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_LOCALE)
        self.commission = baker.make(
            models.LocalCommission, user=self.user)
        school = baker.make(models.School, confirmed=False, 
            local_commission=self.commission)
        self.list_url = reverse('schools')
        self.confirm_url = reverse('school_confirm', args=[school.id])
    
    def test_unauthorized_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(self.confirm_url)
        self.assertRedirects(resp, reverse('home'))

    def test_authorized_requests(self):
        self.user_national = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_NATIONALE)
        perm, created = Permission.objects.get_or_create(
            codename='change_school_status')
        self.user_national.user_permissions.add(perm)
        self.client.force_login(self.user_national)

        # Non-Htmx requests should redirect to schools page
        resp = self.client.get(self.confirm_url, follow=True)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'index.html')

        # Htmx requests should work and return the template
        resp = self.client.get(self.confirm_url, **htmx_headers)
        self.assertEqual(resp.status_code, 200)
        self.assertTemplateUsed(resp, 'partials/school/school_status_change_confirm.html')

        self.client.post(self.confirm_url, **htmx_headers)
        self.assertTrue(models.School.objects.first().confirmed)
        self.assertTemplateUsed(resp, 'partials/school/school_status_change_confirm.html')


class YearPricingUpdateViewTests(TestCase):
    def setUp(self):
        self.user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_NATIONALE)
        self.year = baker.make(
            models.Year, is_current=True, short_name=2023)
        self.inactive_year = baker.make(
            models.Year, is_current=True, short_name='2022')

    def test_unauthorized_requests(self):
        self.client.force_login(self.user)
        resp = self.client.get(reverse('pricing_update', args=[self.year.short_name]))
        self.assertRedirects(resp, reverse('home'))

    def test_authorized_requests(self):
        perm, created = Permission.objects.get_or_create(codename='change_year')
        self.user.user_permissions.add(perm)
        self.client.force_login(self.user)

        # Non-Htmx requests should redirect to home
        resp = self.client.get(reverse('pricing_update', 
            args=[self.year.short_name]))
        self.assertEqual(resp.status_code, 302)
        self.assertRedirects(resp, reverse('home'))

        # HTMX requests
        data = {
            'price_cepe': 8000, 
            'price_bepc': 9000, 
            'price_bac': 10_000,
            'school_fees': 1000,
        }
        resp = self.client.post(
            reverse('pricing_update', 
                args=[self.year.short_name]), data=data, 
                **htmx_headers)
        self.assertEqual(resp.status_code, 302)
        self.year = custom_utils.get_current_year()
        self.assertEqual(self.year.price_bac, 10_000)

    def test_years_not_current(self):
        resp = self.client.get(
            reverse('pricing_update', 
                args=[self.inactive_year.short_name]), 
                **htmx_headers)
        self.assertEqual(resp.status_code, 302)
        self.assertEqual(resp.url, reverse('home'))


# Payments Views Tests
class PaymentsListViewTestCase(TestCase):
    def test_payments_list_view(self):
        # Create a user with the required permissions
        year = baker.make(models.Year, is_current=True, short_name=2023)
        user = baker.make(get_user_model(), 
            role=constants.ROLE_COMMISSION_NATIONALE)
        permission = Permission.objects.get(codename='view_schoolpayment')
        user.user_permissions.add(permission)
        user.save()

        # Create a school and a school payment
        school = baker.make(models.School)
        baker.make(
            models.SchoolPayment, school=school, 
            year=year, _quantity=10)

        # Log in the user and access the view
        self.client.force_login(user)
        response = self.client.get(reverse('payments', args=[year.short_name]))

        # Check that the view returns a 200 status code and that the context is correct
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'full_template.html')
        self.assertEqual(len(response.context['object_list']), 10)
        self.assertEqual(response.context['year'], custom_utils.get_current_year())
        self.assertEqual(response.context['ROLE_ECOLE'], constants.ROLE_ECOLE)
        self.assertEqual(response.context['ROLE_COMMISSION_LOCALE'], constants.ROLE_COMMISSION_LOCALE)
        self.assertEqual(response.context['ROLE_COMMISSION_NATIONALE'], constants.ROLE_COMMISSION_NATIONALE)
        self.assertEqual(response.context['payments_total'], models.SchoolPayment.objects.get_total(user=user, year=custom_utils.get_current_year()))

class PaymentCreateViewTestCase(TestCase):
    def test_payment_create_view(self):
        # Create a user with the required permissions
        year = baker.make(models.Year, is_current=True, short_name=2023)
        user = baker.make(get_user_model(), role=constants.ROLE_COMMISSION_NATIONALE)
        permission, created = Permission.objects.get_or_create(
            codename='add_schoolpayment')
        user.user_permissions.add(permission)
        user.save()

        # Create a school
        commission = baker.make(models.LocalCommission)
        school = baker.make(models.School, local_commission=commission)

        # Log in the user and access the view
        self.client.force_login(user)
        response = self.client.post(reverse('payment_add'), data={
            'date': '01/01/2022',
            'school': school.pk,
            'amount': 1000,
        })

        # Check that the view creates a new SchoolPayment instance
        self.assertEqual(models.SchoolPayment.objects.count(), 1)

# class PaymentUpdateViewTestCase(TestCase):
#     def test_payment_update_view(self):
#         # Create a user with the required permissions
#         user = baker.make(get_user_model(), role=constants.ROLE_COMMISSION_NATIONALE)
#         permission, created = Permission.objects.get_or_create(
#             codename='change_schoolpayment')
#         user.user_permissions.add(permission)
#         user.save()

#         # Create a school and a school payment
#         school = baker.make(models.School)
#         payment = baker.make(models.SchoolPayment, school=school)
#         print(payment.pk, payment.amount)

#         # Log in the user and access the view
#         self.client.force_login(user)
#         response = self.client.post(
#             reverse('payment_edit', args=[payment.pk]), data={
#             'date': '01/01/2022',
#             'school': school.pk,
#             'amount': 2000,
#         }, **htmx_headers)

#         # Check that the view updates the SchoolPayment instance
#         payment = models.SchoolPayment.objects.get(id=payment.pk)
#         self.assertEqual(payment.amount, 2000)

class CandidateSpaceViewTestCase(TestCase):
    def test_candidate_space_view_hmx(self):
        response = self.client.get(reverse('candidate_space'), **htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'partials/student/student_space.html')

    def test_candidate_space_view_not_hmx(self):
        response = self.client.get(reverse('candidate_space'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'full_template.html')


class CandidateInfosCheckViewTestCase(TestCase):
    def setUp(self):
        self.url = reverse('candidate_infos')
        self.year = baker.make(models.Year, is_current=True, short_name=2023)

    def test_candidate_infos_check_view_not_found(self):
        identifier = '123456'
        response = self.client.get(self.url, {'identifier': identifier})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Aucun candidat ne correspond au numéro entré.")

    def test_candidate_infos_check_view_found(self):
        student = baker.make(models.Student, identifier='CEPE20230001')
        enrollment = baker.make(models.Enrollment, year=self.year, student=student)
        identifier = student.identifier
        response = self.client.get(self.url, {'identifier': identifier}, **htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'{enrollment}')


class TestCandidatesListPdf(TestCase):
    
    def setUp(self):
        # Create a user for testing purposes
        self.user = baker.make(
            get_user_model(), role=constants.ROLE_COMMISSION_LOCALE)
        commission = baker.make(models.LocalCommission, user=self.user)
        
    def test_candidates_list_pdf(self):
        # Create a test year and exam
        year = baker.make(models.Year, is_current=True, short_name=2023)
        exam = constants.EXAM_CEPE
        
        # Create test data for Enrollment model
        baker.make(models.Enrollment, year=year, exam=exam, _quantity=10)

        self.client.force_login(self.user)
        url = reverse('candidates_pdf', args=[year.short_name, exam])
        response = self.client.get(url)
        self.assertEqual(response.headers['Content-Type'], 'application/pdf')
        self.assertTrue(response.has_header('Content-Disposition'))


class TestSchoolsListPdf(TestCase):
    
    def setUp(self):
        # Create a user for testing purposes
        self.user = baker.make(
            get_user_model(), role=constants.ROLE_COMMISSION_LOCALE)
        commission = baker.make(models.LocalCommission, user=self.user)
        
    def test_schools_list_pdf(self):
        # Create a test year and exam
        year = baker.make(models.Year, is_current=True, short_name=2023)
        exam = constants.EXAM_CEPE
        
        self.client.force_login(self.user)
        url = reverse('schools_pdf')
        response = self.client.get(url)
        self.assertEqual(response.headers['Content-Type'], 'application/pdf')
        self.assertTrue(response.has_header('Content-Disposition'))


class TestPaymentsListPdf(TestCase):
    
    def setUp(self):
        # Create a user for testing purposes
        self.user = baker.make(
            get_user_model(), role=constants.ROLE_COMMISSION_LOCALE)
        commission = baker.make(models.LocalCommission, user=self.user)
        
    def test_payments_list_pdf(self):
        # Create a test year and exam
        year = baker.make(models.Year, is_current=True, short_name=2023)
        exam = constants.EXAM_CEPE
        
        self.client.force_login(self.user)
        url = reverse('payments_pdf')
        response = self.client.get(url)
        self.assertEqual(response.headers['Content-Type'], 'application/pdf')
        self.assertTrue(response.has_header('Content-Disposition'))


class TestFeesListPdf(TestCase):
    
    def setUp(self):
        # Create a user for testing purposes
        self.user = baker.make(
            get_user_model(), role=constants.ROLE_ECOLE)
        
    def test_fees_list_pdf(self):
        # Create a test year and exam
        year = baker.make(models.Year, is_current=True, short_name=2023)
        
        self.client.force_login(self.user)
        url = reverse('fees_pdf')
        response = self.client.get(url)
        self.assertTrue(response.has_header('Content-Disposition'))