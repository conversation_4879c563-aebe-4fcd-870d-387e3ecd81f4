{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content" enctype=multipart/form-data>
    <div class="modal-header text-success bg-light-green">
        <span class="modal-title fw-bold">
            DEMANDE DE CORRECTION
        </span>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    {% csrf_token %}
    <div class="modal-body">
        <div class="form-row">
            <div class="form-group mb-2 col-md-6 mx-auto">
                <label for="{{ form.table_num.id_for_label }}">N° de table *</label>
                <div class="d-flex"> 
                    {% render_field form.table_num class='form-control'%} 
                    <a href="" class="btn btn-warning btn-sm ml-1" hx-get="{% url 'correction_add' %}" hx-include="[name=table_num]" hx-target="#modal-content">
                        <span data-feather="search"></span>
                    </a>
                </div>
            </div>
        </div>
        <div class="form-row container-fluid">
            <small class="font-weight-bold text-danger ml-3">
                1. Ne pas toucher les informations de la colonne de gauche. <br>
                2. Remplir uniquement les informations qui doivent être corrigées 
                   dans la colonne à droite (en bleu). <br>
                3. Laissez les autres champs vides.
            </small>
        </div>
        <div class="form-row mx-auto">
            <div class="col-xs-6 w-50 text-muted">
                <h5 class="text-muted font-weight-bold">Actuellement</h5>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_last_name_fr.id_for_label }}">Nom en Français *</label>
                    {% render_field form.initial_last_name_fr class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_first_name_fr.id_for_label }}">Prénoms en Français *</label>
                    {% render_field form.initial_first_name_fr class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_full_name_ar.id_for_label }}">Nom complet arabe *</label>
                    {% render_field form.initial_full_name_ar class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_gender.id_for_label }}">Sexe *</label>
                    {% render_field form.initial_gender class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_birth_date.id_for_label }}">Né(e) le *</label>
                    {% render_field form.initial_birth_date type='text' class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_birth_place.id_for_label }}">Lieu *</label>
                    {% render_field form.initial_birth_place class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_birth_place_ar.id_for_label }}">Lieu arabe *</label>
                    {% render_field form.initial_birth_place_ar class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_nationality.id_for_label }}">Nationalité *</label>
                    {% render_field form.initial_nationality class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.initial_exam_type.id_for_label }}">Candidat *</label>
                    {% render_field form.initial_exam_type class='form-control'%}
                </div>
            </div>
            
            <div class="col-xs-6 w-50 text-primary">
                <h5 class="font-weight-bold text-primary font-weight-bold">Corrections</h5>
                <div class="form-group mb-2">
                    <label for="{{ form.new_last_name_fr.id_for_label }}">Nom en Français</label>
                    {% render_field form.new_last_name_fr class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_first_name_fr.id_for_label }}">Prénoms en Français</label>
                    {% render_field form.new_first_name_fr class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_full_name_ar.id_for_label }}">Nom complet arabe</label>
                    {% render_field form.new_full_name_ar class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_gender.id_for_label }}">Sexe</label>
                    {% render_field form.new_gender class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_birth_date.id_for_label }}">Né(e) le</label>
                    {% render_field form.new_birth_date type='date' class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_birth_place.id_for_label }}">Lieu</label>
                    {% render_field form.new_birth_place class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_birth_place_ar.id_for_label }}">Lieu arabe</label>
                    {% render_field form.new_birth_place_ar class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_nationality.id_for_label }}">Nationalité</label>
                    {% render_field form.new_nationality class='form-control'%}
                </div>
                <div class="form-group mb-2">
                    <label for="{{ form.new_exam_type.id_for_label }}">Candidat</label>
                    {% render_field form.new_exam_type class='form-control'%}
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="col border rounded py-2 my-3 bg-warning">
                <div>
                    <label for="{{ form.new_photo.id_for_label }}">Nouvelle photo</label>
                </div>
                {% render_field form.new_photo %}

                <div class="img-div">
                    <img src="" id="imgPreview" height="100px" alt="">
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="col border rounded py-2 my-3 bg-light">
                <div>
                    <label for="{{ form.new_certificate.id_for_label }}">Extrait de Naissance</label>
                </div>
                {% render_field form.new_certificate %}
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success">Enregistrer</button>
        <button class="btn btn-danger" id="submit-btn" data-dismiss="modal">Fermer</button>
    </div>
</form>

<script>
    hideSubmitButtonIfFormValid()

    if (typeof(flatpickr) !== "undefined") {
        flatpickr("#id_initial_birth_date", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "True"
        });
        flatpickr("#id_new_birth_date", {
        dateFormat: "d/m/Y",
        enableTime: false,
        allowInput: true,
        timeZone: "Africa/Abidjan",
        disableMobile: "True"
        });
    }

    if(typeof(feather) != "undefined") feather.replace();

    function previewImage(previewInput, that) {
      const file = that.files[0];
      if (file) {
          let reader = new FileReader();
          reader.onload = function (event) {
              $(previewInput).attr("src", event.target.result);
          };
          reader.readAsDataURL(file);
      }
    };

    $("#id_new_photo").change(function () {
        previewImage("#imgPreview", this)
    });
</script>