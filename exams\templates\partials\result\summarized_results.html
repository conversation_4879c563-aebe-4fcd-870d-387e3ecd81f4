{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Résultats</li>
    </ol>
  </nav>
  <h5 class="font-weight-bold mb-2">RESULTATS {% if center %} DE {{ center}} {% endif %}</h5>

  {% if not display_results %}
  <div class="row">
    <div class="mb-1 col-md-4 col-8">
      Localité:
      <select name="location" id="location" 
        class="form-control" hx-get="{% url 'room_location_centers' %}?url=results" 
        hx-include="[name=exam]"
        hx-target="#center_container">
        <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
        {% for location in locations %}
          <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
        {% empty %}
        {% endfor %}
      </select>
    </div>

    <div class="mb-1 col-md-4 col-4">
      Examen:
      <select name="exam" id="exam" 
        class="form-control" hx-get="{% url 'room_location_centers' %}?url=results" 
        hx-include="[name=location]"
        hx-target="#center_container">
        <option value="cepe" {% if not selected_exam %} selected="selected" {% endif %}>CEPE</option>
        <option value="bepc" {% if selected_exam == 'bepc' %} selected="selected" {% endif %}>BEPC</option>
        <option value="bepc" {% if selected_exam == 'bac' %} selected="selected" {% endif %}>BAC</option>
      </select>
    </div>

  <div class="col-md-4 mb-1" id="center_container">
    <div>Centre:</div>
    <select name="center" id="center" 
            class="form-control" required="required"
            hx-get="{% url 'results' %}" 
            hx-target="#main-content"
            hx-include="[name=exam], [name=location]">
      <option value="0">Sélectionner</option>
      {% for center in centers_list %}
      <option value="{{ center.id }}" 
      {% if selected_center == center.id %} selected {% endif %}>{{ center }}</option>
      {% empty %}
      {% endfor %}
    </select>
  </div>
</div>
{% endif %}

  {% if display_results %}
  <div class="row" id="table-wrapper">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-bordered table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white text-center">PHOTO</th>
                <th scope="col" class="text-white">N° INSCRIPTION</th>
                <th scope="col" class="text-white">N° TABLE</th>
                <th scope="col" class="text-white" style="min-width: 120px">NOM ET PRENOMS</th>
                <th scope="col" class="text-white" style="min-width: 80px">إسم و لقب</th>
                <th scope="col" class="text-white">NE(E) LE</th>
                <th scope="col" class="text-white">POINTS</th>
                <th scope="col" class="text-white">MOYENNE</th>
                <th scope="col" class="text-white">DECISION</th>
              </tr>
            </thead>
            <tbody>
              {% for candidate in candidates %}
              <tr>
                <td class="align-middle text-center" style="min-width: 60px;">
                  {% if candidate.student.photo %}
                    <img data-original="{{ candidate.student.photo.url }}" 
                        alt="1" 
                        class="lazy border img-thumbnail rounded-circle">
                  {% endif %}
                </td>
                <td class="align-middle">{{ candidate.student.identifier }}</td>
                <td class="align-middle">{{ candidate.table_num }}</td>
                <td class="align-middle">{{ candidate.student }}</td>
                <td class="align-middle">{{ candidate.student.full_name_ar }}</td>
                <td class="align-middle">{{ candidate.student.birth_date|date:"d/m/Y" }}</td>
                <td class="align-middle">{{ candidate.total|floatformat:0 }}</td>
                <td class="align-middle">{{ candidate.average|floatformat:2 }}</td>
                <td class="align-middle"><span class="badge badge-pill badge-{% if candidate.average >= MIN_AVERAGE %}success{% else %}danger{% endif %}">
                   {% if candidate.average >= MIN_AVERAGE %} 
                        ADMIS {% else %} 
                        REFUSE 
                        {% endif %}
                      </span>  
                    </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  {% endif %}
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>