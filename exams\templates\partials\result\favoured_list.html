{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Repechages</li>
    </ol>
  </nav>
  <h5 class="font-weight-bold mb-2">ELEVES REPECHES</h5>

  <div class="row" id="table-wrapper">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-bordered table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white text-center">PHOTO</th>
                <th scope="col" class="text-white">N° INSCRIPTION</th>
                <th scope="col" class="text-white">N° TABLE</th>
                <th scope="col" class="text-white" style="min-width: 120px">NOM ET PRENOMS</th>
                <th scope="col" class="text-white" style="min-width: 80px">إسم و لقب</th>
                <th scope="col" class="text-white">NE(E) LE</th>
                <th scope="col" class="text-white">POINTS</th>
                <th scope="col" class="text-white text-left">CENTRE</th>
              </tr>
            </thead>
            <tbody>
              {% for candidate in candidates %}
              <tr>
                <td class="align-middle text-center" style="min-width: 60px;">
                  {% if candidate.student.photo %}
                    <img data-original="{{ candidate.student.photo.url }}" 
                        alt="1" 
                        class="lazy border img-thumbnail rounded-circle">
                  {% endif %}
                </td>
                <td class="align-middle">{{ candidate.student.identifier }}</td>
                <td class="align-middle">{{ candidate.table_num }}</td>
                <td class="align-middle">{{ candidate.student }}</td>
                <td class="align-middle">{{ candidate.student.full_name_ar }}</td>
                <td class="align-middle">{{ candidate.student.birth_date|date:"d/m/Y" }}</td>
                <td class="align-middle">{{ candidate.total|floatformat:0 }}</td>
                <td>{{ candidate.center }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>