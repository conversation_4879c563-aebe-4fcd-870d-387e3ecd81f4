<nav aria-label="Page navigation" {% if include_items %}hx-include="{{ include_items }}"{% endif %} {% if exam_value %} hx-vals='{"exam": "{{ exam }}"}' {% endif %} {% if vals_items %} hx-vals='{"niveau":"{{ level }}", "filtre": "{{ request.GET.filtre }}"}' {% endif %}>
    <ul class="pagination">
        <li class="page-item{% if not page_obj.has_previous %} active disabled{% endif %}">
            {% if page_obj.has_previous %}
                <a class="page-link" href="?page=1" hx-get="?page=1" hx-target="#main-content">1</a>
            {% else %}
                <span class="page-link">1</span>
            {% endif %}
        </li>

        {% if page_obj.number > 3 %}
            <li class="page-item disabled"><span class="page-link">...</span></li>
        {% endif %}

        {% for i in page_obj.paginator.page_range %}
            {% if i == 1 or i == page_obj.paginator.num_pages %}
            {% elif i > page_obj.number|add:"-4" and i < page_obj.number|add:"4" %}
                {# Display dynamic range of page numbers around the current page #}
                <li class="page-item{% if i == page_obj.number %} active{% endif %}">
                    <a class="page-link" href="?page={{ i }}" hx-get="?page={{ i }}" hx-target="#main-content">{{ i }}</a>
                </li>
            {% endif %}
        {% endfor %}

        {% if page_obj.number|add:"2" < page_obj.paginator.num_pages %}
            <li class="page-item disabled"><span class="page-link">...</span></li>
        {% endif %}

        <li class="page-item{% if not page_obj.has_next %} active disabled{% endif %}">
            {% if page_obj.has_next %}
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" hx-get="?page={{ page_obj.paginator.num_pages }}" hx-target="#main-content">{{ page_obj.paginator.num_pages }}</a>
            {% else %}
                <span class="page-link">{{ page_obj.paginator.num_pages }}</span>
            {% endif %}
        </li>
    </ul>
</nav>

<div class="text-muted">
    Affichage de {{ page_obj.start_index }} à {{ page_obj.end_index }} sur {{ page_obj.paginator.count }}
</div>