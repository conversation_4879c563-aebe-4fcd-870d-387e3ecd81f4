from django.test import TestCase
from django.contrib.auth import get_user_model
from exams import models
from model_bakery import baker
from .. import custom_utils, constants


class CustomUtilsMethodsTests(TestCase):
    def setUp(self):
        years = baker.make(models.Year, 4, is_current=False)
        self.active_year = baker.make(models.Year, is_current=True, 
            price_cepe=500, price_bepc=1000, price_bac=2000)
        self.local_commission = baker.make(models.LocalCommission)
        self.commission_school1 = baker.make(models.School, 
            local_commission=self.local_commission)
        self.commission_school2 = baker.make(models.School, 
            local_commission=self.local_commission)

        other_commission = baker.make(models.LocalCommission)
        other_school = baker.make(models.School, 
            local_commission=other_commission)

        self.user_ecole = get_user_model().objects.create(
            username='test', password='123', school=self.commission_school1, 
            role=constants.ROLE_ECOLE)
        self.user_ecole2 = get_user_model().objects.create(
            username='testother2', password='123', 
            school=self.commission_school2, 
            role=constants.ROLE_ECOLE)
        self.user_local = get_user_model().objects.create(
            username='test2', password='123', 
            role=constants.ROLE_COMMISSION_LOCALE, 
            localcommission=self.local_commission)
        self.other_user = get_user_model().objects.create(
            username='test3', password='123', 
            role=constants.ROLE_COMMISSION_LOCALE, 
            localcommission=self.local_commission)
        self.user_national = get_user_model().objects.create(
            username='test4', password='123', 
            role=constants.ROLE_COMMISSION_NATIONALE)
        
        enrollment1 = baker.make(
            models.Enrollment, 2, school=self.commission_school1, 
            agent=self.user_ecole, year=self.active_year, 
            exam=constants.EXAM_CEPE, active=True, confirmed=True
        )
        enrollment2 = baker.make(
            models.Enrollment, 5, school=self.commission_school1, 
            agent=self.user_ecole, year=self.active_year, 
            exam=constants.EXAM_BEPC, active=True, confirmed=True
        )
        enrollment3 = baker.make(
            models.Enrollment, school=self.commission_school1, 
            agent=self.user_ecole, year=self.active_year, 
            exam=constants.EXAM_BAC, active=True, confirmed=True
        )
        enrollment4 = baker.make(
            models.Enrollment, school=self.commission_school1, 
            agent=self.user_ecole, year=self.active_year, 
            exam=constants.EXAM_BAC, active=True, confirmed=True
        )
        self.school2_enrollment = baker.make(
            models.Enrollment, school=self.commission_school2, 
            agent=self.user_ecole2, year=self.active_year, 
            exam=constants.EXAM_CEPE, active=True, confirmed=True
        )
        self.school2_enrollment2 = baker.make(
            models.Enrollment, school=self.commission_school2, 
            agent=self.user_ecole2, year=self.active_year, 
            exam=constants.EXAM_BAC, active=True, confirmed=True
        )
        
    def test_get_current_year_method(self):
        self.assertEqual(models.Year.objects.count(), 5)
        active_year = custom_utils.get_current_year()
        self.assertEqual(active_year.short_name, self.active_year.short_name)

    def get_fees(self, exam, user=None):
        return custom_utils.get_exam_fees( 
            year=self.active_year, exam=exam, user=user
        )
    
    def test_get_exam_fees_method_for_school(self): 
        self.client.force_login(self.user_ecole)
        #  For self.user_ecole
        # CEPE EXAM
        candidates_count = models.Enrollment.candidates.count_students(
            year=self.active_year, user=self.user_ecole, 
            exam=constants.EXAM_CEPE
        )
        result = candidates_count * self.active_year.price_cepe
        self.assertEqual(
            self.get_fees(constants.EXAM_CEPE, self.user_ecole), result)
        
        # BEPC EXAM
        candidates_count = models.Enrollment.candidates.count_students(
            year=self.active_year, user=self.user_ecole, 
            exam=constants.EXAM_BEPC
        )
        result = candidates_count * self.active_year.price_bepc
        self.assertEqual(
            self.get_fees(constants.EXAM_BEPC, self.user_ecole), result)
        
        # BAC EXAM
        candidates_count = models.Enrollment.candidates.count_students(
            year=self.active_year, user=self.user_ecole, 
            exam=constants.EXAM_BAC
        )
        result = candidates_count * self.active_year.price_bac
        self.assertEqual(
            self.get_fees(constants.EXAM_BAC, self.user_ecole), result)

    def test_exam_fees_method_for_commission_locale(self):
        self.client.force_login(self.user_local)
        
        # CEPE EXAM
        candidates_count_raw = models.Enrollment.objects.filter(
            confirmed=True, exam=constants.EXAM_CEPE, active=True, 
            year=self.active_year, 
            school__local_commission=self.local_commission).count()

        candidates_count = models.Enrollment.candidates.CEPE(
            year=self.active_year, user=self.user_local
        ).count()
        self.assertEqual(candidates_count, candidates_count_raw)

        expected_result = candidates_count * self.active_year.price_cepe
        self.assertEqual(
            self.get_fees(constants.EXAM_CEPE,
            user=self.user_local), expected_result)
        
        # BEPC EXAM
        candidates_count = models.Enrollment.candidates.BEPC(
            year=self.active_year, user=self.user_local, 
        ).count()
        expected_result = candidates_count * self.active_year.price_bepc
        self.assertEqual(
            self.get_fees(constants.EXAM_BEPC,
            user=self.user_local), expected_result)
        
        # BAC EXAM
        candidates_count = models.Enrollment.candidates.BAC(
            year=self.active_year, user=self.user_local, 
        ).count()
        expected_result = candidates_count * self.active_year.price_bac
        self.assertEqual(
            self.get_fees(constants.EXAM_BAC, 
            user=self.user_local), expected_result)
    
    def test_exam_fees_method_for_commission_nationale(self):
        self.client.force_login(self.user_national)

        # CEPE EXAM
        candidates_count = models.Enrollment.objects.filter(
            confirmed=True, exam=constants.EXAM_CEPE, active=True, 
            year=self.active_year).count()
        expected_result = candidates_count * self.active_year.price_cepe
        self.assertEqual(
            self.get_fees(constants.EXAM_CEPE, user=self.user_national), 
            expected_result)
        
        # BEPC EXAM
        candidates_count = models.Enrollment.objects.filter(
            confirmed=True, exam=constants.EXAM_BEPC, active=True, 
            year=self.active_year).count()
        expected_result = candidates_count * self.active_year.price_bepc
        self.assertEqual(
            self.get_fees(constants.EXAM_BEPC, user=self.user_national), 
            expected_result)
        
        # CEPE EXAM
        candidates_count = models.Enrollment.objects.filter(
            confirmed=True, exam=constants.EXAM_BAC, active=True, 
            year=self.active_year).count()
        expected_result = candidates_count * self.active_year.price_bac
        self.assertEqual(
            self.get_fees(constants.EXAM_BAC, user=self.user_national), 
            expected_result)
        
    def test_generate_identifier_method(self):
        student_id = self.school2_enrollment.student.id
        identifier = custom_utils.generate_identifier(student_id, constants.EXAM_CEPE)
        expected = f"{constants.EXAM_CEPE.upper()}" + \
                   f"{custom_utils.get_current_year().short_name}" + \
                   f"{str(student_id).rjust(constants.IDENTIFIER_DIGITS_COUNT, '0')}"
        self.assertEqual(identifier, expected)
        
