{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Salles</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      <strong class="text-muted">
      LISTE DES MENTIONS
      </strong>
    </h5>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
    {% if perms.exams.add_distinction %}
      <div>
        <a class="btn btn-success text-white"
           hx-get="{% url 'distinction_add' %}"
           hx-target="#dialog">
           + Ajouter une mention</a>
      </div>
      {% endif %}
</div>

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white" style="max-width: 30px">NOTE</th>
                <th scope="col" class="text-white">MENTION</th>
                <th scope="col" class="text-white">ABBREVIATION</th>
                <th scope="col" class="text-white">TRADUCTION</th>
                <th scope="col" class="text-white align-left">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for distinction in distinctions %}
              <tr>
                <td><strong>{{ distinction.average|floatformat:0 }}</strong></td>
                <td>{{ distinction.name }}</td>
                <td>{{ distinction.short_name }}</td>
                <td >{{ distinction.translation }}</td>
                <td>
                    <button class="btn btn-secondary btn-sm" 
                            hx-get="{% url 'distinction_edit' distinction.id %}" 
                            hx-target="#dialog"><i data-feather="edit"></i></button>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>