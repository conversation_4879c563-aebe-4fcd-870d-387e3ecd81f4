<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Liste des écoles et leurs centres</h5>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a class="nav-link {% if exam == 'cepe' %}active{% endif %}" 
                                       href="{% url 'school_centers' %}?exam=cepe"
                                       hx-get="{% url 'school_centers' %}?exam=cepe" 
                                       hx-target="#main-content">CEPE</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {% if exam == 'bepc' %}active{% endif %}" 
                                       href="{% url 'school_centers' %}?exam=bepc"
                                       hx-get="{% url 'school_centers' %}?exam=bepc" 
                                       hx-target="#main-content">BEPC</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {% if exam == 'bac' %}active{% endif %}" 
                                       href="{% url 'school_centers' %}?exam=bac"
                                       hx-get="{% url 'school_centers' %}?exam=bac" 
                                       hx-target="#main-content">BAC</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and filters -->
                    <div class="row mb-3">
                        <div class="col-lg-3 col-md-6 mb-2">
                            <div class="input-group">
                                <input type="text" class="form-control" 
                                       placeholder="Rechercher..."
                                       name="search" id="search-input"
                                       {% if search %}value="{{ search }}"{% endif %}
                                       hx-get="{% url 'school_centers' %}" 
                                       hx-trigger="keyup changed delay:500ms, search"
                                       hx-target="#main-content"
                                       hx-include="[name='location'],[name='per_page']"
                                       hx-vals='{"exam": "{{ exam }}"}'>
                                <div class="input-group-append">
                                    <button class="btn btn-primary btn-sm" type="button">
                                        <span data-feather="search" class="feather-16 align-middle"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% if request.user.role == ROLE_COMMISSION_NATIONALE %}
                        <div class="col-lg-3 col-md-6 mb-2">
                            <select class="form-control" name="location"
                                    hx-get="{% url 'school_centers' %}" 
                                    hx-trigger="change"
                                    hx-target="#main-content"
                                    hx-include="[name='search'],[name='per_page']"
                                    hx-vals='{"exam": "{{ exam }}"}'>
                                <option value="">-- Toutes les localités --</option>
                                {% for location in locations %}
                                <option value="{{ location.id }}" {% if selected_location == location.id %}selected{% endif %}>
                                    {{ location.location }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}
                        <div class="col-lg-2 col-md-4 mb-2">
                            <select class="form-control" name="per_page"
                                    hx-get="{% url 'school_centers' %}" 
                                    hx-trigger="change"
                                    hx-target="#main-content"
                                    hx-include="[name='search'],[name='location']"
                                    hx-vals='{"exam": "{{ exam }}"}'>
                                <option value="10" {% if per_page == '10' %}selected{% endif %}>10 par page</option>
                                <option value="25" {% if per_page == '25' %}selected{% endif %}>25 par page</option>
                                <option value="50" {% if per_page == '50' %}selected{% endif %}>50 par page</option>
                                <option value="100" {% if per_page == '100' %}selected{% endif %}>100 par page</option>
                            </select>
                        </div>
                    </div>

                    {% if search %}
                    <div class="alert alert-info">
                        <span data-feather="info" class="feather-16 align-middle"></span> {{ result_found }} résultat(s) pour "{{ search }}"
                        <a href="{% url 'school_centers' %}?exam={{ exam }}" 
                           hx-get="{% url 'school_centers' %}?exam={{ exam }}" 
                           hx-target="#main-content" 
                           class="float-right">
                            <span data-feather="x" class="feather-16 align-middle"></span> Effacer
                        </a>
                    </div>
                    {% endif %}

                    <!-- Schools Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>École</th>
                                    <th>Localité</th>
                                    <th>Candidats</th>
                                    <th>Centre</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for school in schools %}
                                <tr>
                                    <td>{{ school.identifier }}</td>
                                    <td>{{ school.name|upper }}</td>
                                    <td>{{ school.local_commission.location }}</td>
                                    <td>{{ school.student_count }}</td>
                                    <td>
                                        {% if school.has_center %}
                                            <span class="badge badge-success">
                                                {{ school.center_identifier|upper }}
                                            </span>
                                        {% else %}
                                            <span class="badge badge-warning">AUCUN</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if school.has_center %}
                                            <a href="{% url 'school_center_cancel' school.id exam %}" 
                                               class="btn btn-sm btn-danger"
                                               hx-get="{% url 'school_center_cancel' school.id exam %}"
                                               hx-target="#dialog">
                                                <span data-feather="trash-2" class="feather-16 align-middle"></span> Annuler
                                            </a>
                                        {% else %}
                                            <a href="{% url 'school_center_assign' school.id exam %}" 
                                               class="btn btn-sm btn-primary"
                                               hx-get="{% url 'school_center_assign' school.id exam %}"
                                               hx-target="#dialog"
                                               data-toggle="modal" data-target="#modal">
                                                <span data-feather="link" class="feather-16 align-middle"></span> Assigner
                                            </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">Aucune école trouvée</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <div class="pagination justify-content-center mt-4">
                        <span class="step-links">
                            {% if page_obj.has_previous %}
                                <a class="btn btn-sm btn-outline-secondary" 
                                   href="?page=1&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-get="{% url 'school_centers' %}?page=1&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-target="#main-content">
                                    <span data-feather="chevrons-left" class="feather-16 align-middle"></span>
                                </a>
                                <a class="btn btn-sm btn-outline-secondary" 
                                   href="?page={{ page_obj.previous_page_number }}&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-get="{% url 'school_centers' %}?page={{ page_obj.previous_page_number }}&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-target="#main-content">
                                    <span data-feather="chevron-left" class="feather-16 align-middle"></span>
                                </a>
                            {% endif %}

                            <span class="current-page mx-2">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>

                            {% if page_obj.has_next %}
                                <a class="btn btn-sm btn-outline-secondary" 
                                   href="?page={{ page_obj.next_page_number }}&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-get="{% url 'school_centers' %}?page={{ page_obj.next_page_number }}&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-target="#main-content">
                                    <span data-feather="chevron-right" class="feather-16 align-middle"></span>
                                </a>
                                <a class="btn btn-sm btn-outline-secondary" 
                                   href="?page={{ page_obj.paginator.num_pages }}&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-get="{% url 'school_centers' %}?page={{ page_obj.paginator.num_pages }}&exam={{ exam }}{% if search %}&search={{ search }}{% endif %}{% if selected_location %}&location={{ selected_location }}{% endif %}{% if per_page %}&per_page={{ per_page }}{% endif %}"
                                   hx-target="#main-content">
                                    <span data-feather="chevrons-right" class="feather-16 align-middle"></span>
                                </a>
                            {% endif %}
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    if (typeof(feather) !== 'undefined') {
        feather.replace();
    }
</script>