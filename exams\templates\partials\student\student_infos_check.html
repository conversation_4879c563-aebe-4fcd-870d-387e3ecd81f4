<div class="container d-flex flex-column align-items-center text-dark">
{% if enrollment %}
    {% if enrollment.student.photo %}
    <img src="{{ enrollment.student.photo.url }}" alt="" 
        class="border rounded-circle" height="100px" width="100px">
    {% endif %}
    <h4><strong>{{ enrollment }}</strong></h4>
    <h4>Examen: {{exam }} <strong>({{ exam_type }})</strong></h4>
    <h4>Statut du dossier: <span class="badge {% if enrollment.confirmed %} badge-success {% else %} badge-warning {% endif %}">
        {% if enrollment.confirmed %} VALIDE {% else %} OUVERT {% endif %}
    </span></h4>
    <span>Née le <strong>{{ enrollment.student.birth_date|date:'d/m/Y' }}</strong>
        à <strong>{{ enrollment.student.birth_place }}</strong>
    </span>
    <span>ECOLE <strong>{{ enrollment.school }}</strong></span>
    <span>Sexe <strong>{{ enrollment.student.get_gender_display }}</strong></span>
    <span>Père <strong>{{ enrollment.student.father }}</strong></span>
    <span>Mère <strong>{{ enrollment.student.mother }}</strong></span>
    <span>Contact <strong>{{ enrollment.student.student_phone|default_if_none:'AUCUN' }}</strong></span>
{% else %}
    <p class="text-danger">Désolé. Aucun candidat ne correspond au numéro entré.</p>
{% endif %}
    <div class="my-3">
        <a class="btn btn-primary" 
           href="" hx-get="{% url 'candidate_space' %}" 
           hx-target="#body"> <span data-feather="dollar">
        Effectuer une nouvelle vérification</a>

    </div>
</div>