// HTMX
triggered = false;

document.body.addEventListener('htmx:configRequest', (event) => {
  event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
  triggered = true;
});

// Add active class to clicked nav link and remove for others
$('.sidebar-nav-link').on('click', function() {
  $('.sidebar-nav-link').removeClass('active');
  $(this).addClass('active');
});


// Close sidenav when a nav-link is clicked
document.body.querySelectorAll('.sidebar-navitem-close-on-click').forEach(element => {
  element.addEventListener('click', (e) =>{
    sidebar = document.querySelector('#sidebar')
     if(sidebar !== null && sidebar.classList.contains('in')) sidebar.classList.remove('in')
  })
});

function loadDatatable() {
  $.fn.dataTable.ext.errMode = 'none';
  $('#datatable').DataTable({
    error: function (xhr, error, thrown) {
      console.log('Datatables', error);
    },
    drawCallback: function() {
     $('.lazy:lt(4)').each(function() {
        $(this).attr('src', $(this).data('original')).removeClass('lazy');
     });
      $('img.lazy').lazyload({
        load: function() { $(this).removeClass("lazyload"); },
      });
      feather.replace();
      htmx.process(document.body);
    },
  });
}

(function () {
    htmx.on("htmx:afterSwap", (e) => {
      if (document.body.classList.contains('pace-running')) {
        document.body.classList.remove('pace-running');
      };
      loadDatatable();
      
      // Response targeting #dialog => show the modal
      if (e.detail.target.id == "dialog") {
        $("#modal").modal("show")
      };
      let photoInput = document.getElementById('id_photo');
      if (photoInput !== null) {
        reduceImageSize('id_photo', 600, 600, true);
      }
    })
  
    htmx.on("htmx:beforeSwap", (e) => {
      // Empty response targeting #dialog => hide the modal
      if (e.detail.target.id == "dialog" && !e.detail.xhr.response) {
        $("#modal").modal("hide")
        e.detail.shouldSwap = false
      }
    })
  
    // Remove dialog content after hiding
    $("#modal").on("hidden.bs.modal", () => {
      $("#dialog").empty()
    })
  })();

$(document).ready(function() {
    feather.replace();

    window.addEventListener('popstate', function(event) {
      location.replace(location.href);
    });

    $.extend( $.fn.dataTable.defaults, {
      language: {
          "sProcessing": "Traitement en cours...",
          "sSearch": "Rechercher&nbsp;:",
          "sLengthMenu": "Afficher _MENU_ &eacute;l&eacute;ments",
          "sInfo": "Affichage de l'&eacute;l&eacute;ment _START_ &agrave; _END_ sur _TOTAL_ &eacute;l&eacute;ments",
          "sInfoEmpty": "Affichage de l'&eacute;l&eacute;ment 0 &agrave; 0 sur 0 &eacute;l&eacute;ments",
          "sInfoFiltered": "(filtr&eacute; de _MAX_ &eacute;l&eacute;ments au total)",
          "sInfoPostFix": "",
          "sLoadingRecords": "Chargement en cours...",
          "sZeroRecords": "Aucun &eacute;l&eacute;ment &agrave; afficher",
          "sEmptyTable": "Aucune donn&eacute;e disponible dans le tableau",
          "oPaginate": {
              "sFirst": "Premier",
              "sPrevious": "Pr&eacute;c&eacute;dent",
              "sNext": "Suivant",
              "sLast": "Dernier"
          },
          "oAria": {
              "sSortAscending": ": activer pour trier la colonne par ordre croissant",
              "sSortDescending": ": activer pour trier la colonne par ordre d&eacute;croissant"
          }
      }
  });

  loadDatatable()
})

// Bottom Navigation
links = document.querySelectorAll('.bottom-nav-link')
links.forEach(link => {
  link.addEventListener('click', (ev) => {
    links.forEach(li => {
      li.classList.remove('active');
    })
    ev.target.closest('a').classList.add('active');
  })
}
)

function hideSubmitButtonIfFormValid(){
  $('#modal-content').on('submit', function() {
    if (this.checkValidity()) {
      $('#submit-btn').addClass('d-none');
      $('#spinner').removeClass('d-none'); 
      this.submit();
    }
    return false;
  });
}

function reduceImageSize(inputId, maxWidth, maxHeight, isPhoto) {
  const fileInput = document.getElementById(inputId);
  fileInput.addEventListener('change', (event) => {
    const file = event.target.files[0];
    const reader = new FileReader();
    
    const fileSize = file.size / 1024;
    if ((isPhoto && fileSize <= 50) || (!isPhoto && fileSize <= 100)) {
    return;
    } else if (isPhoto && fileSize <= 400) {
    maxWidth = 300;
    maxHeight = 300;
    }
    console.log('Initial size', fileSize);

    reader.onload = (readerEvent) => {
    const image = new Image();
  
    image.onload = () => {
      const canvas = document.createElement('canvas');
      let width = image.width;
      let height = image.height;
  
      if (width > height && width > maxWidth) {
      height *= maxWidth / width;
      width = maxWidth;
      } else if (height > maxWidth) {
      width *= maxHeight / height;
      height = maxHeight;
      }
  
      canvas.width = width;
      canvas.height = height;
  
      const context = canvas.getContext('2d');
      context.drawImage(image, 0, 0, width, height);
  
      const dataUrl = canvas.toDataURL(file.type);
      const newFile = dataURLtoFile(dataUrl, file.name);
      
      // Create a new FileList object with the resized file
      const newFileList = createFileList(newFile);

      // Replace the entire FileList in the file input
      fileInput.files = newFileList;

    // Replace the initial image with the resized version
    // fileInput.files[0] = newFile;
      fileInput.files = newFileList;
      console.log('New size:', newFile.size / 1024)
    };
  
    image.src = readerEvent.target.result;
    };
  
    reader.readAsDataURL(file);
  });
  }
  
  // Helper function to convert data URL to a File object
  function dataURLtoFile(dataUrl, fileName) {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new File([u8arr], fileName, { type: mime });
  }

// Helper function to create a new FileList object
function createFileList(...files) {
  const dataTransfer = new DataTransfer();
  for (const file of files) {
  dataTransfer.items.add(file);
  }
  return dataTransfer.files;
}