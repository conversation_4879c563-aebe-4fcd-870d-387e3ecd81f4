<div class="container-fluid mt-2">
  <a href="#" hx-get="{% url 'correction_centers'  %}?exam={{ exam }}" hx-target="#main-content" class="btn btn-sm"><span data-feather="arrow-left"></span> Retour</a>
  <h5 class="mb-3">Liste des centres du centre de correction de {{ correction_center }}</h5>

  <table class="table table-sm table-bordered table-striped">
    <thead>
      <tr>
        <th>Centre</th>
        <th>Localite</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      {% for center in centers %}
      <tr>
        <td>{{ center }}</td>
        <td>{{ center.location }}</td>
        <td>a</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<script>
  if (typeof(feather) !== "undefined") {
    feather.replace()
  }
</script>