{% load static %}

<style>
  #body {
    padding-bottom: 0 !important;
  }
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #28fc67 0%, #764ba2 100%);
    padding: 2rem 1rem;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.12), 0 4px 8px rgba(0,0,0,0.06);
    max-width: 480px;
    width: 100%;
    transition: transform 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.login-card:hover {
    transform: translateY(-5px);
}

.brand-logo {
    width: 100px; /* Reduced from 120px */
    height: 100px; /* Reduced from 120px */
    border-radius: 50%;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 0.75rem; /* Reduced from 1.5rem */
    animation: logoPulse 2s infinite;
}

.card-header {
    padding: 1rem 1.5rem 0.5rem; /* Reduced padding */
}

.header-title {
    font-size: 1.25rem; /* Slightly smaller */
    margin-bottom: 0.5rem; /* Reduced margin */
}

.card-body {
    padding: 0.75rem 1.5rem 1.5rem; /* Adjusted padding */
}

.material-btn {
    border-radius: 8px;
    padding: 0.75rem; /* Reduced from 1rem */
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    margin: 0.35rem 0; /* Reduced from 0.75rem */
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.material-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.material-btn:active {
    transform: translateY(1px) scale(0.98);
}

.material-btn:nth-child(1) {
    animation: staggerButtons 0.4s ease-out 0.2s both;
}

.material-btn:nth-child(2) {
    animation: staggerButtons 0.4s ease-out 0.3s both;
}

.material-btn:nth-child(3) {
    animation: staggerButtons 0.4s ease-out 0.4s both;
}

.material-btn:nth-child(4) {
    animation: staggerButtons 0.4s ease-out 0.5s both;
}

/* Loading state animation */
.material-btn[disabled] {
    position: relative;
    overflow: hidden;
}

.material-btn[disabled]::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: loading 1.5s infinite;
}

.material-btn::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 0.5s;
}

.material-btn:active::after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
}

/* Update the text-center div at the top */
.login-card .text-center.pt-4 {
    padding-top: 1rem !important; /* Reduced from default */
}

.login-card .text-center h4 {
    margin-bottom: 0.75rem; /* Reduced margin */
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes staggerButtons {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes logoPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(71, 118, 230, 0.6);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(71, 118, 230, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(71, 118, 230, 0);
    }
}

@keyframes loading {
    from {
        left: -100%;
    }
    to {
        left: 100%;
    }
}
</style>
<div class="login-page d-flex justify-content-center align-items-center">
    <div class="login-card">
        <div class="text-center pt-4">
            <img src="{% static 'images/logo.jpg' %}" alt="" class="brand-logo"> 
        </div>
        
        <div class="card-header text-center">
            <div class="d-flex flex-column align-items-center justify-content-center">
                <h4 class="mb-2 font-weight-bold text-primary">DIRECTION DES ECOLES CHERIFLA</h4>
                <span class="header-title">
                    <i data-feather="book-open" class="text-primary mr-2" style="width: 24px; height: 24px;"></i>
                    GESTION DES EXAMENS
                </span>
              </div>
              <div class="header-title font-weight-bold text-success">SESSION {{ active_year }}</div>
        </div>

        <div class="card-body px-4 pb-4">
            <a href="" 
               hx-get="{% url 'login' %}?espace=ecole" 
               hx-push-url="{% url 'login' %}?espace=ecole" 
               hx-target="#body"
               class="material-btn btn btn-primary w-100">
               <span data-feather="home"></span> ESPACE ECOLE
            </a>

            <a href="" 
               hx-get="{% url 'login' %}?espace=commission_locale" 
               hx-push-url="{% url 'login' %}?espace=commission_locale" 
               hx-target="#body"
               class="material-btn btn btn-secondary w-100">
               <span data-feather="link"></span> COMMISSION LOCALE
            </a>

            <a href="" 
               hx-get="{% url 'login' %}?espace=commission_nationale" 
               hx-push-url="{% url 'login' %}?espace=commission_nationale" 
               hx-target="#body"
               class="material-btn btn btn-success w-100">
               <span data-feather="grid"></span> COMMISSION NATIONALE
            </a>

            <a href="" 
               class="material-btn btn btn-warning w-100" 
               hx-get="{% url 'candidate_space' %}"
               hx-push-url="{% url 'candidate_space' %}"
               hx-target="#body">
               <span data-feather="users"></span> ESPACE CANDIDAT
            </a>
            <a href="" 
               class="material-btn btn btn-info w-100" 
               hx-get="{% url 'login' %}?espace=rh"
               hx-push-url="{% url 'login' %}?espace=rh"
               hx-target="#body">
               <span data-feather="briefcase"></span> RESSOURCES HUMAINES
            </a>
        </div>
    </div>
</div>

<script>
    if (typeof(feather) !== 'undefined') {
        feather.replace({
            width: 18,
            height: 18
        });
    }
</script>