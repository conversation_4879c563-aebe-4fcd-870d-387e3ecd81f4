{% extends 'components/modal.html' %}
{% load humanize %}

{% block modal_title %} Candidat au {{ enrollment.get_exam_display }} {% endblock %}

{% block modal_body %}
<div class="modal-body mx-2 row">
    <ul class="list-group col-md-6">
        <li class="list-group-item bg-secondary text-white"><span>Identité et Filliation</li>
        <li class="list-group-item"><span class="text-muted">Identifiant:</span> {{ enrollment.student.identifier|default_if_none:'' }}</li>
        <li class="list-group-item"><span class="text-muted">Nom et Prénoms:</span> {{ enrollment.student }}</li>
        <li class="list-group-item"><span class="text-muted">Traduction:</span> {{ enrollment.student.full_name_ar|default_if_none:'' }}</li>
        <li class="list-group-item"><span class="text-muted">Sexe:</span> {{ enrollment.student.get_gender_display }}</li>
        <li class="list-group-item"><span class="text-muted">Né le:</span> {{ enrollment.student.birth_date|date:'d/m/Y'|default_if_none:'' }}</li>
        <li class="list-group-item"><span class="text-muted">Lieu de Naissance:</span> {{ enrollment.student.birth_place|default_if_none:'' }}</li>
        <li class="list-group-item"><span class="text-muted">Nom du Père:</span> {{ enrollment.student.father|default_if_none:'' }}</li>
        <li class="list-group-item"><span class="text-muted">Nom de la Mère:</span> {{ enrollment.student.mother|default_if_none:'' }}</li>
    </ul>
    <ul class="list-group col-md-6">
        <li class="list-group-item bg-secondary text-white"><span>Inscription {{ selected_year }}</li>
        <li class="list-group-item"><span class="text-muted">Examen: </span> {{ enrollment.get_exam_display }} </li>
        <li class="list-group-item"><span class="text-muted">Candidat: </span> {{ enrollment.get_exam_type_display }} </li>
        <li class="list-group-item"><span class="text-muted">Inscrit le: </span> {{ enrollment.date_created|date:'d/m/Y à h:m' }}</li>
        <li class="list-group-item"><span class="text-muted">Ecole: </span> {{ enrollment.school|upper }}</li>
    </ul>
</div>
{% endblock %}