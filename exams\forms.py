from datetime import datetime
from django import forms
from django.db.models.aggregates import Sum, Avg, Count
from django.db.models import Q
from django.forms import ModelForm
from django.core.validators import MinLengthValidator
from project_utils import custom_utils, constants
from . import models


class StudentForm(ModelForm):
    exam_type = forms.ChoiceField(
        choices=constants.EXAM_TYPES_CHOICES,
        initial=constants.EXAM_TYPE_OFFICIEL)

    class Meta:
        model = models.Student
        fields = [
            'matricule', 'first_name', 'last_name', 'gender', 'student_phone',
            'full_name_ar', 'birth_date', 'birth_place', 'birth_place_ar',
            'father', 'mother', 'student_phone', 'photo',
            'certificate', 'exam_type', 'nationality'
        ]


class SchoolListForm(forms.Form):
    school = forms.ModelChoiceField(
        queryset=models.School.objects.all(),
        required=True)
    exam = forms.ChoiceField(choices=constants.EXAM_CHOICES)

    def __init__(self, user, exam, *args, **kwargs):
        super().__init__(*args, **kwargs)
        qs = user.localcommission.school_set.all()
        self.fields['school'].queryset = qs
        self.fields['exam'].choices = ((exam, exam.upper()),)


class SchoolEditForm(forms.ModelForm):
    class Meta:
        model = models.School
        fields = [
             'school_type', 'school_cycle', 'school_status', 'teachers_count',
             'name', 'name_ar', 'local_commission',
             'director', 'phone', 'location', 'drena_obj', 'iepp'
        ]

    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user.role == constants.ROLE_COMMISSION_LOCALE:
                qs = models.LocalCommission.objects.filter(
                    id=user.localcommission.id
                )
                self.fields['local_commission'].queryset = qs
                self.fields['local_commission'].initial = user.localcommission


class LocalCommissionEditForm(forms.ModelForm):
    director = forms.CharField(max_length=255)

    class Meta:
        model = models.LocalCommission
        fields = ['location', 'director', 'phone']

    def clean_director(self):
        data = self.cleaned_data['director']
        if not str(data).strip():
            raise forms.ValidationError('Ne peut être vide')
        return data


class SchoolFeesForm(forms.ModelForm):
    school = forms.ModelChoiceField(
        queryset=models.School.objects.all(),
        required=True)

    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        qs = models.School.objects.get_for_user(user)
        self.fields['school'].queryset = qs

    class Meta:
        model = models.SchoolFees
        fields = ['school', 'amount']


class CenterAddForm(forms.ModelForm):
    schools = forms.ModelMultipleChoiceField(
        queryset=models.School.objects.all(),
        widget=forms.SelectMultiple,
        required=False
    )

    class Meta:
        model = models.Center
        fields = [
            'school', 'location', 'identifier',
            'schools', 'director', 'phone', 'exam'
        ]


class CenterEditForm(forms.ModelForm):
    schools = forms.ModelMultipleChoiceField(
        queryset=models.School.objects.all(),
        widget=forms.SelectMultiple,
        required=False
    )

    def __init__(self, location, exam=constants.EXAM_CEPE, *args, **kwargs):
        super().__init__(*args, **kwargs)
        year = custom_utils.get_current_year()
        qs = models.School.objects.annotate(
            candidates=Count('enrollment', filter=Q(enrollment__exam=exam) & Q(enrollment__confirmed=True) & Q(enrollment__year=year))
        ).filter(candidates__gt=0).exclude(center__isnull=False)
        self.fields['schools'].queryset = qs.filter(local_commission=location)

    class Meta:
        model = models.Center
        fields = [
            'school', 'location', 'identifier',
            'schools', 'director', 'phone', 'exam'
        ]


class LocationSchoolsForm(forms.ModelForm):
    def __init__(self, location, only_schools_having_students=True, *args, **kwargs):
        super().__init__(*args, **kwargs)
        qs = models.School.objects.annotate(students=Count('enrollment')) \
            .filter(local_commission=location)
        if only_schools_having_students:
            qs = qs.filter(students__gt=0)

        self.fields['school'].queryset = qs

    class Meta:
        model = models.Center
        fields = ['school']


class LocationSchoolsMultiSelect(forms.ModelForm):
    schools = forms.ModelMultipleChoiceField(
        queryset=models.School.objects.all(),
        widget=forms.SelectMultiple,
        required=False
    )

    def __init__(self, commission, exam=constants.EXAM_CEPE, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Note: Forms may be instantiated without request context
        year = custom_utils.get_current_year()
        this_centers = models.Center.objects.filter(exam=exam,
            year=year)
        print(year, exam)
        qs = models.School.objects.annotate(students=Count('enrollment', filter=Q(enrollment__exam=exam) & Q(enrollment__year=year)))
        qs = qs.filter(students__gt=0) \
            .exclude(school_centers__in=this_centers)
        self.fields['schools'].queryset = qs

    class Meta:
        model = models.Center
        fields = ['schools']


class LocationsForm(forms.Form):
    location = forms.ModelChoiceField(
        queryset=models.LocalCommission.objects.all())


class RoomForm(forms.ModelForm):
    center = forms.ModelChoiceField(
        queryset=models.Center.objects.all(),
        required=True)
    location = forms.ModelChoiceField(
        queryset=models.LocalCommission.objects.all(),
        required=False, blank=True
    )
    exam = forms.ChoiceField(
        choices=constants.EXAM_CHOICES, required=False,
        initial=constants.EXAM_CEPE)

    class Meta:
        model = models.Room
        fields = ['location', 'center', 'number', 'capacity']


class BatchRoomForm(forms.Form):
    center = forms.ModelChoiceField(
        queryset=models.Center.objects.all(),
        required=True,
        label='Centre')
    location = forms.ModelChoiceField(
        queryset=models.LocalCommission.objects.all(),
        required=False,
        label='Localité'
    )
    exam = forms.ChoiceField(
        choices=constants.EXAM_CHOICES,
        required=False,
        initial=constants.EXAM_CEPE,
        label='Examen')
    num_rooms = forms.IntegerField(
        min_value=1,
        required=True,
        label='Nombre de salles à créer')
    students_per_room = forms.IntegerField(
        min_value=1,
        required=True,
        label='Nombre d\'élèves par salle')
    starting_number = forms.IntegerField(
        min_value=1,
        required=True,
        initial=1,
        label='Numéro de départ')


class LocationCenterForm(forms.Form):
    center = forms.ModelChoiceField(
        queryset=models.LocalCommission.objects.all()
    )

    def __init__(self, location, exam=constants.EXAM_CEPE, *args, **kwargs):
        super().__init__(*args, **kwargs)
        queryset = models.Center.objects.filter(
            location__id=int(location), exam=exam,
            # Note: Forms may be instantiated without request context
            year=custom_utils.get_current_year())
        self.fields['center'].queryset = queryset


class StudentGradesEditForm(forms.Form):
    table_num = forms.CharField(max_length=7)
    center = forms.ModelChoiceField(queryset=models.Center.objects.all())

    def __init__(self, user, exam=constants.EXAM_CEPE, table_num=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        commission = user.localcommission
        self.exam = exam
        self.fields['center'].queryset = models.Center.objects.get_for_year(
            # Note: Forms may be instantiated without request context
            year=custom_utils.get_current_year(), user=user, exam=exam,
            annotate=False, for_correction=True)
        
        user_location = None
        if user.role == constants.ROLE_COMMISSION_LOCALE:
            user_location = user.localcommission

        subjects = models.Subject.objects.get_queryset(exam=exam, location=user_location)
        for subject in subjects:
            self.fields[f'grade_{subject.id}'] = forms.FloatField(
                label=subject.name, initial='', help_text=subject.translation,
                max_value=20, min_value=0)

    def save_grades(self):
        cd = self.cleaned_data
        table_num = cd['table_num']
        enrollment = models.Enrollment.objects \
            .prefetch_related('grade_set') \
            .filter(table_num=table_num)
        if enrollment.exists():
            subjects = models.Subject.objects.get_queryset(self.exam)
            enrollment = enrollment.first()
            grades_dict = {grade.subject_id: grade for grade in enrollment.grade_set.all()}
            subjects_dict = {subject.name.lower(): subject.id for subject in subjects}
            grades_to_create = []
            for field in self:
                if field.name.lower().startswith('grade_'):
                    subject_name = field.label.lower()
                    subject_id = subjects_dict.get(subject_name)
                    if not subject_id:
                        continue
                    grade = grades_dict.get(subject_id)
                    if grade is None:
                        grades_to_create.append(models.Grade(
                            enrollment=enrollment,
                            subject_id=subject_id,
                            value=float(field.value())
                        ))
                    else:
                        grade.value = int(field.value())
                        grade.save(update_fields=['value'])
            if grades_to_create:
                models.Grade.objects.bulk_create(grades_to_create)

            if grades_dict:
                total = sum(g.value for g in grades_dict.values() if g.value is not None)
            else:
                total = enrollment.grade_set.aggregate(
                    total=Sum('value')
                )['total']
            enrollment.total = total
            enrollment.average = total / len(subjects_dict)

            # Update annual average
            custom_utils.update_annual_average(enrollment)
            enrollment.save(update_fields=['total', 'average', 'gen_average'])


class ExcelUploadForm(forms.Form):
    excel_file = forms.FileField()


class StudentIdentifierInputForm(forms.Form):
    identifier = forms.CharField(min_length=6)


class CorrectionForm(forms.ModelForm):
    class Meta:
        model = models.StudentCorrection
        fields = [
            'table_num',
            'initial_last_name_fr',
            'initial_first_name_fr',
            'initial_full_name_ar',
            'initial_gender',
            'initial_exam_type',
            'initial_birth_date',
            'initial_birth_place',
            'initial_birth_place_ar',
            'initial_nationality',
            'new_last_name_fr',
            'new_first_name_fr',
            'new_full_name_ar',
            'new_gender',
            'new_exam_type',
            'new_birth_date',
            'new_birth_place',
            'new_birth_place_ar',
            'new_nationality',
            'new_certificate',
            'new_photo',
        ]

    def __init__(self, user, table_num=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        year = custom_utils.get_current_year()
        enrollment = None
        qs = models.Enrollment.candidates.get_candidates(
            year=year, user=user
        ) \
            .select_related('student') \
            .filter(center__isnull=False, table_num=table_num)

        if qs.exists():
            enrollment = qs.first()
            self.initial['table_num'] = table_num
            self.initial['initial_last_name_fr'] = enrollment.student.last_name
            self.initial['initial_first_name_fr'] = enrollment.student.first_name
            self.initial['initial_full_name_ar'] = enrollment.student.full_name_ar
            self.initial['initial_gender'] = enrollment.student.gender
            self.initial['initial_birth_date'] = enrollment.student.birth_date
            self.initial['initial_birth_place'] = enrollment.student.birth_place
            self.initial['initial_birth_place_ar'] = enrollment.student.birth_place_ar
            self.initial['initial_exam_type'] = enrollment.exam_type
            self.initial['initial_nationality'] = enrollment.student.nationality

            self.fields['table_num'].widget.attrs['readOnly'] = True
            self.fields['initial_last_name_fr'].widget.attrs['readOnly'] = True
            self.fields['initial_first_name_fr'].widget.attrs['readOnly'] = True
            self.fields['initial_full_name_ar'].widget.attrs['readOnly'] = True
            self.fields['initial_gender'].widget.attrs['readOnly'] = True
            self.fields['initial_birth_date'].widget.attrs['readOnly'] = True
            self.fields['initial_birth_place'].widget.attrs['readOnly'] = True
            self.fields['initial_birth_place_ar'].widget.attrs['readOnly'] = True
            self.fields['initial_exam_type'].widget.attrs['readOnly'] = True
            self.fields['initial_nationality'].widget.attrs['readOnly'] = True


class SchoolDRENAForm(forms.ModelForm):
    class Meta:
        model = models.School
        fields = ['drena_obj', 'iepp']


class SchoolInformationForm(forms.ModelForm):
    """Form for school information step in wizard"""
    class Meta:
        model = models.School
        fields = [
            'name', 'name_ar', 'school_type', 'school_cycle', 'school_status',
            'drena_obj', 'iepp', 'teachers_count', 'director', 'phone'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'onchange': 'this.value = this.value.toUpperCase();',
            }),
            'name_ar': forms.TextInput(attrs={
                'class': 'form-control',
                'onchange': 'this.value = this.value.toUpperCase();',
            }),
            'school_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'school_status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'drena_obj': forms.Select(attrs={
                'class': 'form-control'
            }),
            'iepp': forms.TextInput(attrs={
                'class': 'form-control',
                'onchange': 'this.value = this.value.toUpperCase();',
            }),
            'teachers_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
            }),
            'director': forms.TextInput(attrs={
                'class': 'form-control',
                'onchange': 'this.value = this.value.toUpperCase();',
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'maxlength': '10',
            }),
        }


class SchoolStatisticsForm(forms.ModelForm):
    """Form for school statistics step in wizard"""
    class Meta:
        model = models.SchoolStatistics
        fields = ['levels_count', 'students_count', 'boys_count', 'girls_count']
        widgets = {
            'levels_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
            }),
            'students_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
            }),
            'boys_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
            }),
            'girls_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
            }),
        }

    def clean_students_count(self):
        students_count = self.cleaned_data.get('students_count')
        if students_count is not None and students_count <= 0:
            raise forms.ValidationError("Le nombre d'élèves doit être supérieur à 0")
        return students_count

    def clean(self):
        cleaned_data = super().clean()
        students_count = cleaned_data.get('students_count', 0)
        boys_count = cleaned_data.get('boys_count', 0)
        girls_count = cleaned_data.get('girls_count', 0)

        if boys_count + girls_count != students_count:
            raise forms.ValidationError(
                "Le nombre total d'élèves doit être égal à la somme des garçons et des filles."
            )

        return cleaned_data


class StudentCardToCandidateForm(forms.ModelForm):
    exam = forms.ChoiceField(
        choices=constants.EXAM_CHOICES,
        initial=constants.EXAM_CEPE
    )

    class Meta:
        model = models.StudentCard
        fields = ['exam']


class TransferEditForm(forms.ModelForm):
    def __init__(self, user,  instance=None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if instance:
            self.fields['enrollment'].queryset = models.Enrollment.filter(pk=instance.enrollment_id)
            self.fields['old_school'].queryset = models.School.filter(pk=instance.old_school_id)
            self.fields['school'].queryset = models.School.filter(pk=instance.school_id)

            self.fields['enrollment'].initial = models.Enrollment.filter(pk=instance.enrollment_id).first()
            self.fields['old_school'].initial = models.School.filter(pk=instance.old_school_id).first()
            self.fields['school'].initial = models.School.filter(pk=instance.school_id).first()

    class Meta:
        model = models.TransferRequest
        fields = ['enrollment', 'old_school', 'school']


class CorrectionCenterForm(forms.ModelForm):
    location_filter = forms.ModelMultipleChoiceField(
        queryset=models.LocalCommission.objects.all(),
        required=False)
    centers = forms.ModelMultipleChoiceField(queryset=models.Center.objects.all())
    class Meta:
        model = models.CorrectionCenter
        fields = [
            'exam', 'correction_center'
        ]


class CorrectionCentersFieldForm(forms.Form):
    centers = forms.ModelMultipleChoiceField(queryset=models.Center.objects.all())