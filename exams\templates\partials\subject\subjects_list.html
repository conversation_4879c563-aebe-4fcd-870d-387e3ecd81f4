{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Salles</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      <strong class="text-muted">
      LISTE DES MATIERES
      </strong>
    </h5>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
    {% if perms.exams.add_subject %}
      <div>
        <a class="btn btn-success text-white"
           hx-get="{% url 'subject_add' %}"
           hx-target="#dialog"
           hx-include="[name=exam]">
           + Ajouter une matière</a>
      </div>
      {% endif %}
</div>
<div class="container col-md-4 my-2">
  <label for="exam">Examen</label>
  <select name="exam" id="exam" class="form-control" 
          hx-get="" hx-target="#main-content"
          hx-push-url="">
    <option value="cepe" {% if exam == 'cepe' %} selected {% endif %}>CEPE</option>
    <option value="bepc" {% if exam == 'bepc' %} selected {% endif %}>BEPC</option>
    <option value="bac" {% if exam == 'bac' %} selected {% endif %}>BAC</option>
  </select>
</div>

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                  <th scope="col" class="text-white">#</th>
                  <th scope="col" class="text-white" style="min-width: 100px">MATIERE</th>
                <th scope="col" class="text-white">TRADUCTION</th>
                <th scope="col" class="text-white">COEF.</th>
                <th scope="col" class="text-white">STATUT</th>
                <th scope="col" class="text-white align-left">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for subject in subjects %}
              <tr>
                <td><strong>{{ subject.order }}</strong></td>
                <td>{{ subject }}</td>
                <td>{{ subject.translation }}</td>
                <td >{{ subject.coefficient }}</td>
                <td class="text-center">
                    <span class="badge badge-pill {% if subject.active %} badge-success {% else %} badge-danger{% endif %}">
                        {% if subject.active %} ACTIF {% else %} INACTIF {% endif %}
                    </span> 
                </td>
                <td>
                    <button class="btn btn-secondary btn-sm" 
                            hx-get="{% url 'subject_edit' subject.id %}" 
                            hx-target="#dialog"><i data-feather="edit"></i></button>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>