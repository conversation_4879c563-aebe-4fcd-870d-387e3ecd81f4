{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Centres de correction</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      <strong class="text-muted">
      LISTE DES CENTRES DE CORRECTION 
      </strong>
    </h5>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
  {% if perms.exams.add_correctioncenter %}
      <div>
        <a class="btn btn-sm btn-success text-white"
           hx-get="{% url 'correction_center_add' %}?exam={{ exam_name }}"
           hx-target="#dialog">
           + Ajouter un centre de correction</a>
      </div>
  {% endif %}
   </div>

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">CENTRE DE CORRECTION</th>
                <th scope="col" class="text-white">ELEVES</th>
                <th scope="col" class="text-white" style="min-width: 100px">Voir les centres de composition</th>
                <th scope="col" class="text-white align-left">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for correction_center in correction_centers %}
              <tr>
                <td>{{ correction_center }}</td>
                <td>{{ correction_center.candidates }}</td>
                <td style="min-width: 115px"><a href="" hx-get="{% url 'correction_centers_centers' %}?correction_center={{ correction_center.pk }}" hx-target="#main-content">Voir la liste des centres</a></td>
                <td><a href="" hx-get="{% url 'correction_center_edit' correction_center.pk %}" 
                        class="btn btn-sm btn-warning"
                        hx-target="#dialog"><span data-feather="edit"></span></a></td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>