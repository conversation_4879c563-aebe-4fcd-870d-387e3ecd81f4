{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">{% if subject %} MODIFIER MENTION {{ distinction|upper }} {% else %} AJOUTER MATIERE {{ exam|upper }} {% endif %}</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Infos mention</div>
        </div>
        <div class="form-row">
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.average.id_for_label }}">Note</label>
                 {% render_field form.average class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.name.id_for_label }}">Mention en français *</label>
                 {% render_field form.name class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.short_name.id_for_label }}">Abbréviation *</label>
                 {% render_field form.short_name class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6" id="location_centers">
                <label for="{{ form.translation.id_for_label }}">Traduction *</label>
                 {% render_field form.translation class='form-control' %}
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>