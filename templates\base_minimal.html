<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ school_name }} - Examens{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
            min-height: 100vh;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .text-primary {
            color: #1976d2 !important;
        }

        .bg-primary {
            background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%) !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0d47a1 0%, #01579b 100%);
            box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
        }

        .btn-outline-danger {
            border-color: #dc3545;
            color: #dc3545;
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        
        .alert-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: none;
            color: #0d47a1;
        }
        
        .bg-light {
            background: #f8f9fa !important;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 15px;
            }
            
            .card-body {
                padding: 2rem !important;
            }
            
            .btn-lg {
                padding: 0.75rem 2rem !important;
                font-size: 1rem !important;
            }
            
            h3 {
                font-size: 1.5rem !important;
            }
            
            .card-header {
                padding: 2rem !important;
            }
        }
        
        @media (max-width: 576px) {
            .card-body {
                padding: 1.5rem !important;
            }
            
            .card-header {
                padding: 1.5rem !important;
            }
            
            .btn-lg {
                padding: 0.6rem 1.5rem !important;
                font-size: 0.9rem !important;
            }
            
            h3 {
                font-size: 1.25rem !important;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div id="body">
        {% block content %}{% endblock %}
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Initialize Feather Icons -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
