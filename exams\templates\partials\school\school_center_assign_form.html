{% load widget_tweaks %}
<form class="modal-content" id="modal-content" method="post" action="{% url 'school_center_assign' object.id exam %}">
    {% csrf_token %}
    <div class="modal-header">
        <h5 class="modal-title">Assigner un centre à l'école</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="alert alert-info">
            <span data-feather="info" class="feather-16 align-middle"></span>
            Assigner l'école <strong>{{ object.name|upper }}</strong> à un centre pour l'examen <strong>{{ exam|upper }}</strong>.
        </div>

        <div class="form-group">
            <label for="id_location">Localité:</label>
            <select name="location" id="id_location" class="form-control" 
                    hx-get="{% url 'location_centers_for_school' %}" 
                    hx-target="#id_center" 
                    hx-indicator="#location-spinner"
                    hx-trigger="change"
                    hx-vals='{"exam": "{{ exam }}"}'>
                <option value="">-- Sélectionner une localité --</option>
                {% for loc in locations %}
                <option value="{{ loc.id }}">{{ loc.location }}</option>
                {% endfor %}
            </select>
            <small id="location-spinner" class="htmx-indicator">
                <span data-feather="loader" class="feather-16 spinner"></span> Chargement des centres...
            </small>
        </div>
        
        <div class="form-group">
            <label for="id_center">Centre:</label>
            <select name="center" id="id_center" class="form-control" required>
                <option value="">-- Sélectionner d'abord une localité --</option>
            </select>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
        <button type="submit" class="btn btn-primary">
            <span data-feather="link" class="feather-16 align-middle"></span> Assigner
        </button>
    </div>
</form>

<script>
    if (typeof(feather) !== 'undefined') {
        feather.replace();
    }
</script>
