{% load widget_tweaks %}

<form action="{{request.path}}?exam={{exam}}" method="post" class="container mt-2 col-md-8" style="margin-bottom: 70px;">
    {% csrf_token %}
    <div class="form-row">
        <h3 class="text-muted"><span id="progression">{{ progress }}</span></h3>
    </div>
    <div class="form-row">
        <div class="form-group mb-2 col-md-6">
            <label for="{{ form.center.id_for_label }}">Centre</label>
                {% render_field form.center class='form-control' hx-get='/progression/' hx-target='#progression' %}
        </div>
        <div class="form-group mb-2 col-md-6">
            <label for="{{ form.table_num.id_for_label }}">Numéro de table</label>
            <div class="d-flex">
                {% render_field form.table_num class='form-control' %}

                {% if updating_grade %}
                <a class="btn btn-warning btn-sm ml-1" 
                   id="search_btn"
                   hx-get="{% url 'grade_add' %}?exam={{exam}}" 
                   hx-include="[name=table_num], [name=center]"
                   hx-target="#main-content">
                    <span data-feather="search"></span>Vérifier
                </a>
                {% endif %}
            </div>
            <div class="text-danger pl-1"><small>{{ error }}</small></div>
        </div>
    </div>
    
    {% if not updating_grade %}
    <div class="form-row">
        {% for field in form %}
            {% if not field.name == 'table_num' and not field.name == 'center' %}
            <div class="form-group mb-2 col-md-6">
                <label for="{{ field.id_for_label }}" class="d-flex justify-content-between">
                    <span>{{ field.label }}</span> <span>{{ field.help_text }}</span></label>
                {% render_field field class='form-control' %}
                <div class="text-danger">{{ field.errors }}</div>
            </div>
            {% endif %}
        {% endfor %}
    </div>
    <div class="form-row mt-2">
        <div class="form-group col-md-12">
            <button type="submit" class="btn btn-success btn-block"> Enregistrer</button>
        </div>
    </div>

    {% endif %}

</form>

<script>
    if (typeof(feather) !== 'undefined') {
      feather.replace(); 
    }

    const gradeInputs = document.querySelectorAll('input[name^="grade_"]');
    // Add event listener for focus to each input element
    gradeInputs.forEach(input => {
    input.addEventListener('focus', function() {
        this.select();
    });
    });

    const table_num = document.getElementById('id_table_num');
    const center = document.getElementById('id_center');
    const button = document.getElementById('search_btn');

    table_num.addEventListener('input', function() {
        disableIfEmpty()
    });

    center.addEventListener('input', function() {
        disableIfEmpty()
    });

    function disableIfEmpty() {
        if (table_num.value === '' || center.value === '') {
            button.classList.add('disabled');
        } else {
            button.classList.remove('disabled');
        }
    }
  </script>