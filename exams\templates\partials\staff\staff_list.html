{% load static %}
{% load humanize %}

<div class="container-fluid card mt-2 pt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Enseignants</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">LISTE DU PERSONNEL CHERIFLA</h5>
  </div>
  <div class="container d-flex mb-3 justify-content-between">
    <span class="badge badge-warning p-2">EN COURS: {{ stats.pending }}</span>
    <span class="badge badge-info p-2">IMPRIMEES: {{ stats.manufactured }}</span>
    <span class="badge badge-success p-2">LIVREES: {{ stats.shipped }}</span>
  </div>
  <div class="form-group row">
    <div class="col-8">
      <label for="search" class="pl-2">Rechercher:</label>
      <input type="search" name="search" id="search" 
              class="form-control btn-sm ml-2"
              value="{{ search }}"
              hx-get="{{ request.path }}" hx-target="#main-content">
    </div>
    <div class="col-4">
      <label for="search" class="pl-2">Afficher</label>
      <select name="per_page" id="per_page" class="form-control form-control-sm" hx-get="{{ request.path }}?page={{ page }}&niveau={{ level }}"
              hx-target="#main-content">
        <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 éléments</option>
        <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 éléments</option>
        <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 éléments</option>
        <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 éléments</option>
      </select> 
    </div>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
    {% if perms.exams.add_staff %}
    <div>
      <a class="btn btn-success text-white"
         hx-get="{% url 'staff_add' %}"
         hx-target="#dialog">
         + Ajouter</a>
    </div>
    {% endif %}
  </div>

  <div class="table-responsive">
    <table class="table table-hover table-striped table-sm table-actions">
      <thead>
        <tr class="bg-secondary">
          <th class="text-white">Photo</th>
          <th class="text-white">Matricule</th>
          <th class="text-white">Nom</th>
          <th class="text-white">Prénoms</th>
          <th class="text-white">Sexe</th>
          <th class="text-white">Né(e) le</th>
          <th class="text-white">Emploi</th>
          <th class="text-white">Carte</th>
          <th class="text-white">DRENA</th>
          <th class="text-white">Année</th>
          <th class="text-white">Ecole</th>
          <th class="text-white">Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for staff in object_list %}
        <tr>
          <td class="align-middle text-center" style="min-width: 60px;">
            {% if staff.photo %}
              <img data-original="{{ staff.photo.url }}" 
                  alt="1" 
                  class="lazy border img-thumbnail rounded-circle">
            {% else %}
              <img data-original="{% static 'images/avatar.jpg' %}" 
              alt="1" 
              class="lazy border img-thumbnail rounded-circle">
            {% endif %}
          </td>
          <td class="align-middle">{{ staff.code }}</td>
          <td class="align-middle">{{ staff.last_name }}</td>
          <td class="align-middle">{{ staff.first_name }}</td>
          <td class="align-middle">{{ staff.get_gender_display }}</td>
          <td class="align-middle">{{ staff.birth_date|date:'d/m/Y' }}</td>
          <td class="align-middle">{{ staff.get_job_display }}</td>
          <td class="align-middle" > <span class="badge badge-pill p-1 {% if staff.status == 'P' %} badge-warning {% elif staff.status == 'M' %} badge-info {% elif staff.status == 'S' %} badge-success {% endif %}">{{ staff.get_status_display }}</span></td>
          <td class="align-middle">{{ staff.location.name }}</td>
          <td class="align-middle">{{ staff.year }}</td>
          <td class="align-middle">{{ staff.school }}</td>
          <td class="align-middle">
            {% if perms.exams.change_staff %}
            <a href="#" class="btn btn-sm btn-warning"
               hx-get="{% url 'staff_edit' staff.id %}"
               hx-target="#dialog">
              <i data-feather="edit"></i>
            </a>
            {% endif %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
    {% include 'components/pagination.html' with include_items="[name=per_page]" vals_items=True %}
  </div>
</div>

<script>
  
  $('img.lazy').lazyload({
      load: function() { $(this).removeClass("lazyload"); },
    });

  if (typeof(feather) != "undefined") {
    feather.replace()
  };

</script>