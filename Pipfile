[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "==4.2.1"
mysqlclient = "==2.1.1"
django-htmx = "==1.14.0"
django-widget-tweaks = "==1.4.12"
pillow = "==9.5.0"
model-bakery = "==1.11.0"
django-debug-toolbar = "==4.1.0"
sweetify = "==2.3.1"
fpdf2 = "*"
python-decouple = "==3.8"
gunicorn = "==20.1.0"
whitenoise = "==6.4.0"
django-redis = "==5.2.0"
cloudinary = "==1.33.0"
django-pwa = "==1.1.0"
python-bidi = "==0.4.2"
qrcode = "==7.4.2"
openpyxl = "==3.1.2"
arabic-reshaper = "==3.0.0"
asgiref = "==3.7.2"
async-timeout = "==4.0.2"
certifi = "==2023.5.7"
colorama = "==0.4.6"
defusedxml = "==0.7.1"
django-appconf = "==1.0.5"
django-datatables-view = "==1.20.0"
django-imagekit = "==4.1.0"
et-xmlfile = "==1.1.0"
fonttools = "==4.39.4"
pilkit = "==2.0"
pypng = "==0.20220715.0"
python-dateutil = "==2.8.2"
redis = "==4.5.5"
six = "==1.16.0"
sqlparse = "==0.4.4"
typing-extensions = "==4.6.3"
tzdata = "==2023.3"
urllib3 = "==1.26.16"
requests = "*"
django-import-export = "*"

[dev-packages]

[requires]
python_version = "3.10"
