{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Centres</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      <strong class="text-muted">
      LISTE DES CENTRES 
      {% if user.role == ROLE_COMMISSION_LOCALE %} 
        DE {{ user.localcommission }}
      {% endif %}
      </strong>
    </h5>
  </div>
  <div class="container-fluid p-0 mb-3 row justify-content-between">
  {% if perms.exams.add_center %}
      <div>
        <a class="btn btn-primary text-white mt-2"
           hx-get="{% url 'center_add' %}?exam={{ exam }}"
           hx-target="#dialog">
           <span data-feather="plus" class="feather-16 align-middle"></span>
           Ajouter un centre</a>
      </div>
      <div>
        <a class="btn btn-warning mt-2"
           href="{% url 'centers_list_pdf' %}?exam={{ exam }}">
           <span data-feather="file-text" class="feather-16 align-middle"></span>
           Imprimer centres</a>
      </div>
      <div>
        <a class="btn btn-success text-white mt-2"
           hx-get="{% url 'center_confirm' %}?exam={{ exam }}"
           hx-target="#dialog">
           <span data-feather="check" class="feather-16 align-middle"></span> Valider les centres</a>
      </div>
  {% endif %}
</div>

<!-- Improved Filters UI -->
<div class="card mb-3 shadow-sm" id="filters-card" hx-vals='{"exam": "{{ exam }}"}'>
  <div class="card-header bg-light d-flex justify-content-between align-items-center py-2">
    <h6 class="mb-0">
      <span data-feather="filter" class="feather-16 mr-1"></span>
      Filtres
      <span class="badge badge-primary ml-2" id="active-filters-count">0</span>
    </h6>
    <div>
      <button type="button" class="btn btn-sm btn-outline-primary" id="toggle-filters">
        <span data-feather="chevron-down" id="toggle-icon"></span>
      </button>
      <button type="button" class="btn btn-sm btn-outline-secondary ml-2" id="clear-filters" title="Effacer les filtres">
        <span data-feather="x" class="feather-16"></span>
      </button>
    </div>
  </div>
  <div class="card-body py-3 collapsed" id="filters-body">
    <div class="row">
      <div class="col-md-4 col-sm-6 mb-2">
        <div class="form-group mb-2">
          <label for="location" class="small font-weight-bold mb-1">Localité</label>
          <select name="location" id="location" class="form-control form-control-sm" 
                  hx-get="{{ request.path }}?page={{ page }}"
                  hx-target="#main-content" 
                  hx-include=".form-control">
            {% if user.role != ROLE_COMMISSION_LOCALE %} 
            <option value="" {% if not selected_location %} selected="selected" {% endif %}>Toutes les localités</option>
            {% endif %}
            {% for location in locations %}
              <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-6 mb-2">
        <div class="form-group mb-2">
          <label for="search" class="small font-weight-bold mb-1">Rechercher</label>
          <input type="search" name="search" id="search" 
                class="form-control form-control-sm"
                value="{{ search }}"
                placeholder="Nom, code..."
                hx-get="{{ request.path }}" 
                hx-target="#main-content"
                hx-include=".form-control">
        </div>
      </div>
      <div class="col-md-2 col-sm-6 mb-2">
        <div class="form-group mb-2">
          <label for="per_page" class="small font-weight-bold mb-1">Afficher</label>
          <select name="per_page" id="per_page" class="form-control form-control-sm" 
                  hx-get="{{ request.path }}?page={{ page }}"
                  hx-target="#main-content" 
                  hx-include=".form-control">
            <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10</option>
            <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25</option>
            <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50</option>
            <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100</option>
          </select>
        </div>
      </div>
      <!-- Space for additional filters -->
      <div class="col-md-2 col-sm-6 mb-2">
        <!-- Example of how to add a new filter -->
        <!-- <div class="form-group mb-2">
          <label for="status" class="small font-weight-bold mb-1">Statut</label>
          <select name="status" id="status" class="form-control form-control-sm">
            <option value="">Tous les statuts</option>
            <option value="valid">Validé</option>
            <option value="pending">En révision</option>
          </select>
        </div> -->
      </div>
    </div>
    <!-- Applied Filters Tags -->
    <div id="applied-filters" class="mt-2 d-none">
      <div class="d-flex flex-wrap" id="filter-tags"></div>
    </div>
  </div>
</div>

<!-- Script for Filters UI -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toggleFilters = document.getElementById('toggle-filters');
    const filtersBody = document.getElementById('filters-body');
    const toggleIcon = document.getElementById('toggle-icon');
    const clearFilters = document.getElementById('clear-filters');
    const filterInputs = document.querySelectorAll('#filters-body select, #filters-body input');
    const activeFiltersCount = document.getElementById('active-filters-count');
    const appliedFilters = document.getElementById('applied-filters');
    const filterTags = document.getElementById('filter-tags');
    
    // Initialize
    updateFilterCount();
    
    // Toggle filters visibility
    toggleFilters.addEventListener('click', function() {
      if (filtersBody.classList.contains('show')) {
        filtersBody.classList.remove('show');
        toggleIcon.setAttribute('data-feather', 'chevron-down');
      } else {
        filtersBody.classList.add('show');
        toggleIcon.setAttribute('data-feather', 'chevron-up');
      }
      feather.replace();
    });
    
    // Count active filters and update badge
    function updateFilterCount() {
      let count = 0;
      filterTags.innerHTML = '';
      
      filterInputs.forEach(input => {
        if ((input.type === 'select-one' && input.value && input.selectedIndex > 0) || 
            (input.type === 'search' && input.value.trim() !== '')) {
          count++;
          
          // Create filter tag
          const tag = document.createElement('div');
          tag.className = 'badge badge-light mr-2 mb-1 p-2';
          
          let labelText = '';
          if (input.type === 'select-one') {
            labelText = input.options[input.selectedIndex].text;
            tag.innerText = `${input.previousElementSibling.innerText}: ${labelText}`;
          } else {
            tag.innerText = `Recherche: ${input.value}`;
          }
          
          // Add remove button
          const removeBtn = document.createElement('span');
          removeBtn.className = 'ml-1 cursor-pointer';
          removeBtn.innerHTML = '&times;';
          removeBtn.style.cursor = 'pointer';
          removeBtn.onclick = function() {
            if (input.type === 'select-one') {
              input.selectedIndex = 0;
            } else {
              input.value = '';
            }
            // Trigger the filter
            if (input.getAttribute('hx-get')) {
              htmx.trigger(input, 'change');
            }
            updateFilterCount();
          };
          
          tag.appendChild(removeBtn);
          filterTags.appendChild(tag);
        }
      });
      
      activeFiltersCount.innerText = count;
      
      if (count > 0) {
        appliedFilters.classList.remove('d-none');
        // Auto-expand filters if there are active ones
        filtersBody.classList.add('show');
        toggleIcon.setAttribute('data-feather', 'chevron-up');
      } else {
        appliedFilters.classList.add('d-none');
      }
      
      feather.replace();
    }
    
    // Listen for changes on filter inputs
    filterInputs.forEach(input => {
      input.addEventListener('change', updateFilterCount);
      if (input.type === 'search') {
        input.addEventListener('keyup', function(e) {
          if (e.key === 'Enter') {
            updateFilterCount();
          }
        });
      }
    });
    
    // Clear all filters
    clearFilters.addEventListener('click', function() {
      filterInputs.forEach(input => {
        if (input.type === 'select-one') {
          input.selectedIndex = 0;
        } else {
          input.value = '';
        }
      });
      
      // Trigger filter on the first input to refresh data
      if (filterInputs.length > 0 && filterInputs[0].getAttribute('hx-get')) {
        htmx.trigger(filterInputs[0], 'change');
      }
      
      updateFilterCount();
    });
    
    // Initialize with filters expanded if there are active filters
    if (parseInt(activeFiltersCount.innerText) > 0) {
      filtersBody.classList.add('show');
      toggleIcon.setAttribute('data-feather', 'chevron-up');
      feather.replace();
    }
  });
</script>

{% if location_schools.exists %}
  <div class="alert alert-warning">
    <span class="font-weight-bold">Ces écoles de la localité ont des candidats mais n'ont pas de centres:</span>
    <ol>
      {% for school in location_schools %}
        <li>{{ school }}</li>
      {% endfor %}
    </ol>
  </div>
{% endif %}
  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">CODE</th>
                <th scope="col" class="text-white">LOCALITE</th>
                <th scope="col" class="text-white" style="min-width: 100px">CENTRE</th>
                <th scope="col" class="text-white">ECOLES</th>
                <th scope="col" class="text-white">SALLES</th>
                <th scope="col" class="text-white">CAPACITE <br/>SALLES</th>
                <th scope="col" class="text-white">CANDIDATS <br/>VALIDES</th>
                <th scope="col" class="text-white">CANDIDATS <br/>RECUS</th>
                <th scope="col" class="text-white">STATUT</th>
                <th scope="col" class="text-white">COMMENTAIRE</th>

                <th scope="col" class="text-white align-left">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for center in centers %}
              <tr>
                <td>{{ center.id }}</td>
                <td style="min-width: 115px">{{ center.location }}</td>
                <td>{{ center }}</td>
                <td><strong>{{ center.schools }}</strong></td>
                <td><strong>{{ center.rooms }}</strong></td>
                <td><strong>{{ center.rooms_capacity|default:'0' }}</strong></td>
                <td><strong>{% if exam == 'cepe' %} {{ center.count_cepe_candidates|default:'0' }} {% elif exam == 'bepc' %} {{ center.count_bepc_candidates|default:'0' }} {% elif exam == 'bac' %} {{ center.count_bac_candidates|default:'0' }} {% endif %}</strong></td>
                <td><strong>{{ center.candidates }}</strong></td>
                <td class="align-middle text-center"><span class="badge {% if center.complete %} badge-success {% else %} badge-warning {% endif %}">
                  {% if center.complete %} VALIDE {% else %} EN REVISION {% endif %}
                </span></td>
                <td>{{ center.comment|default:'' }}</td>
                <td class="text-center">
                  <div class="dropdown">
                    <button class="btn btn-primary btn-sm dropdown" type="button" data-toggle="dropdown" aria-expanded="false">
                    <i data-feather="more-horizontal"></i>
                    </button>
                    <div class="dropdown-menu">
                      {% if not center.complete or center.candidates != center.rooms_count or user.role == 'CN' %}
                        {% if perms.exams.change_center %}
                          <a class="dropdown-item" href="#"
                          hx-get="{% url 'center_edit' center.id %}?exam={{exam}}"
                          hx-target="#dialog">Modifier</a>
                          <div class="dropdown-divider"></div>
                        {% endif %}
                        {% if perms.exams.delete_center %}
                          <a class="dropdown-item" href="#"
                          hx-get="{% url 'center_delete' center.id %}"
                          hx-target="#dialog"
                          hx-include="[name=exam]">Supprimer</a>
                          <div class="dropdown-divider"></div>
                        {% endif %}
                      {% endif %}
                      <a class="dropdown-item" href="{% url 'center_candidates' center.id exam %}">
                          Listes des candidats
                        </a>
                      <a class="dropdown-item" href="{% url 'center_rooms' center.id exam %}">
                          Listes par salle
                        </a>
                      <a class="dropdown-item" 
                           href="{% url 'center_candidates' center.id exam %}?document=convocations">
                          Convocations
                        </a>
                      <a class="dropdown-item" 
                           href="{% url 'center_candidates' center.id exam %}?document=anonymat">
                          Fiches anonymats 
                        </a>
                      <a class="dropdown-item" 
                           href="{% url 'center_candidates' center.id exam %}?document=fiches_table">
                          Fiches de table 
                        </a>
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items="[name=per_page], [name=search]" exam_value="{{ exam }}" %}

        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>