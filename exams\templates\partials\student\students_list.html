{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item"><a href="#">Accueil</a></li>
      <li class="breadcrumb-item active" aria-current="page">Elèves {{ selected_year }}</li>
    </ol>
  </nav>

  {% if enrollment_closed %}
  <div class="mx-2">
    <div class="alert alert-warning" role="alert">
      <span data-feather="alert-triangle"></span>
      <small>Les inscriptions sont fermées!</small>
    </div>
  </div>
  {% endif %}

  <div class="pb-3 mt-3">
    <h3 class="fw-bold">Candidats au {{ exam }} {{ selected_year }} 
      {% if school %} DE L'ECOLE {{ school|upper }}
      {% elif location %}
      DE {{ location }}
      {% endif %}
    </h3>
  </div>

  <div class="d-flex flex-row justify-content-between mb-3">
    {% if perms.exams.add_student %}
        <a class="btn btn-success text-white"
           hx-get="{% url 'candidate_add' exam %}"
           hx-target="#dialog">
           <i data-feather="plus"></i>
           Ajouter</a>
    {% elif perms.exams.change_student_status %}
        <a class="btn btn-success text-white"
          hx-get="{% url 'confirm_school_candidates' exam %}"
          hx-target="#dialog">
          <i data-feather="check-square"></i>
          Valider les dossiers</a>
    {% endif %}

    {% if user.role != ROLE_COMMISSION_NATIONALE %}
      <div class="dropdown">
        <button class="btn btn-warning dropdown" type="button" 
                data-toggle="dropdown" aria-expanded="false">
          <i data-feather="file-text"></i> Imprimer liste
        </button>
        <div class="dropdown-menu">
            <a class="dropdown-item" 
              href="{% url 'candidates_pdf' exam %}"> Candidats inscrits </a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" 
              href="{% url 'candidates_pdf' exam %}?valides"> Dossiers Validés </a>
        </div>
      </div>
    {% endif %}
    </div>

    <div class="d-flex material-form justify-content-between row mb-2 {% if user.role == ROLE_ECOLE %}d-none{% endif %}">
      <div class="wrapper col-6 {% if not user.role == ROLE_COMMISSION_NATIONALE %} d-none {% endif %}">
        Localité:
        <select name="locations_select" id="locations_select" 
          class="form-control" hx-get="{% url 'candidates' exam %}" 
          hx-target="#main-content">
          <option value="0" {% if not selected_location %} selected="selected" {% endif %}>Sélectionner</option>
          {% for location in locations_list %}
            <option value="{{ location.id }}" {% if selected_location == location.id %} selected="selected" {% endif %}>{{ location }}</option>
          {% empty %}
          {% endfor %}
        </select>
      </div>
      
      <div class="wrapper col-6 {% if user.role == ROLE_ECOLE %} d-none {% endif %}">
        Ecole:
        <select name="schools_select" id="schools_select" 
        class="form-control"
        hx-get="{% url 'candidates' exam %}" hx-target="#main-content" hx-include="[name=locations_select]">
          <option value="0">Sélectionner</option>
          {% for school in schools_list %}
          <option value="{{ school.id }}" 
          {% if selected_school == school.id %} selected {% endif %}>{{ school }}</option>
          {% empty %}
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="d-flex flex-row justify-content-around my-2">
      <span class="badge badge-pill badge-primary p-2">Garçons: {{ boys|stringformat:'02d' }}</span>
      <span class="badge badge-pill badge-danger p-2">Filles: {{ girls|stringformat:'02d' }}</span>
      <span class="badge badge-pill badge-info p-2">Total: {{ object_count|stringformat:'02d' }}</span>
      <span class="badge badge-pill badge-success p-2">Validés: {{ students_valid|stringformat:'02d' }}</span>
    </div>

    <div class="form-group row material-form">
      <div class="col-8">
        <label for="search" class="pl-2">Rechercher:</label>
        <input type="search" name="search" id="search" 
                class="form-control btn-sm ml-2"
                value="{{ search }}"
                hx-get="{{ request.path }}" 
                hx-target="#main-content"
                hx-include="[name=per_page], [name=locations_select], [name=schools_select]">
      </div>
      <div class="col-4">
        <label for="per_page" class="pl-2">Afficher</label>
        <select name="per_page" id="per_page" class="form-control form-control-sm" hx-get="{{ request.path }}?page={{ page }}"
                hx-target="#main-content" hx-include="[name=search], [name=locations_select], [name=schools_select]">
          <option value="10" {% if per_page|floatformat:'0' == '10' %} selected="selected" {% endif %}>10 éléments</option>
          <option value="25" {% if per_page|floatformat:'0' == '25' %} selected="selected" {% endif %}>25 éléments</option>
          <option value="50" {% if per_page|floatformat:'0' == '50' %} selected="selected" {% endif %}>50 éléments</option>
          <option value="100" {% if per_page|floatformat:'0' == '100' %} selected="selected" {% endif %}>100 éléments</option>
        </select> 
      </div>
    </div>
  
    {% if result_found %}
    <div class="alert alert-warning font-weight-bold d-flex justify-content-between border">
      <span><span data-feather="filter" class="feather-16 align-middle mr-2"></span> {{ result_found }} résultat(s)</span>
    </div>
    {% endif %}
  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm d-table-sm table-actions table-striped table-bordered table-hover mb-0" data-table hx-boost="true">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white text-center">Photo</th>
                <th scope="col" class="text-white">Matricule ou ID</th>
                <th scope="col" class="text-white">Nom et Prénoms</th>
                <th scope="col" class="text-white">Sexe</th>
                <th scope="col" class="text-white">Né(e) le</th>
                <th scope="col" class="text-white text-center">Dossier</th>
                <th scope="col" class="text-white text-center">Carte S.</th>
                {% if user.role != ROLE_ECOLE %}
                <th scope="col" class="text-white">Ecole</th>
                {% endif %}
                <th scope="col" class="text-white">Actions</th>
              </tr>
            </thead>
            <tbody id="tbody">
                {% for enrollment in enrollments %}
                <tr>
                  <td class="align-middle text-center" style="min-width: 60px;">
                    {% if enrollment.student.photo %}
                      <img data-original="{{ enrollment.student.photo.url }}" 
                          alt="1" 
                          class="lazy border img-thumbnail rounded-circle">
                    {% endif %}
                  </td>
                  <td class="align-middle"><a href="" 
                    hx-get="{% url 'candidate_detail' enrollment.id %}"
                    hx-target="#dialog">{% if enrollment.student.matricule %} {{ enrollment.student.matricule|default_if_none:'-' }} {% else %} {{ enrollment.student.identifier }} {% endif %}</a> {% if enrollment.student.full_name_ar %} <br> {{ enrollment.student.full_name_ar }} {% endif %}</td>
                  <td class="align-middle">{{ enrollment.student }} </td>
                  <td class="align-middle">{{ enrollment.student.gender }}</td>
                  <td class="align-middle">{{ enrollment.student.birth_date|date:'d/m/Y'|default_if_none:'' }} 
                    {% if enrollment.student.birth_place %} <br> {{ enrollment.student.birth_place|upper }} {% endif %}
                  </td>
                  
                  <td class="align-middle text-center"><span class="badge {% if enrollment.confirmed %} badge-success {% else %} badge-warning {% endif %}">
                    {% if enrollment.confirmed %} VALIDE {% else %} OUVERT {% endif %}
                  </span></td>
                  <td class="align-middle text-center">
                    <span class="badge {% if enrollment.card_status == 'P' %} badge-warning {% elif enrollment.card_status == 'M' %} badge-info {% elif enrollment.card_status == 'S' %} badge-success {% else %} badge-secondary {% endif %}">
                      {% if enrollment.card_status == 'P' %} EN COURS {% elif enrollment.card_status == 'M' %} IMPRIMEE {% elif enrollment.card_status == 'S' %} LIVREE {% else %} EN ATTENTE {% endif %}
                    </span>
                  </td>
                  
                  {% if user.role != ROLE_ECOLE %}
                  <td class="align-middle">{{ enrollment.school|upper }}</td>
                  {% endif %}
                  <td class="align-middle text-center">
                    <div class="dropdown">
                      {% if perms.exams.change_student %}
                        <button class="btn btn-warning btn-sm" 
                                hx-get="{% url 'candidate_edit' exam enrollment.id %}" 
                                hx-target="#dialog"><i data-feather="edit"></i></button>
                      {% endif %}

                      {% if perms.exams.change_student_status %}
                        <button class="btn btn-info btn-sm" 
                                hx-get="{% url 'confirm_status' exam enrollment.id %}" 
                                hx-target="#dialog"
                                {% if enrollment.confirmed %} disabled="disabled" {% endif %}><i data-feather="check-square"></i>Valider</button>
                      {% endif %}
                      {% if perms.exams.add_transferrequest %}
                        <button class="btn btn-secondary btn-sm" 
                                hx-get="{% url 'transfert_create' %}?enrollment_id={{ enrollment.pk }}&exam={{ exam }}&activ_year={{ active_year }}" 
                                hx-target="#dialog"><i data-feather="arrow-up-right"></i>Trans.</button>
                      {% endif %}

                      <button class="btn btn-primary btn-sm dropdown" type="button" 
                        data-toggle="dropdown" aria-expanded="false">
                        <i data-feather="more-horizontal"></i>
                      </button>
                      <div class="dropdown-menu">
                        {% if not enrollment.confirmed %}
                          {% if perms.exams.delete_student %}
                            <a class="dropdown-item" href="" hx-get="{% url 'candidate_delete' enrollment.id %}" 
                              hx-target="#dialog">Supprimer l'élève</a>
                         {% endif %}
                        {% elif perms.exams.change_school_status %}
                          <a class="dropdown-item" 
                              href="" 
                              hx-get="{% url 'cancel_status' enrollment.id %}" 
                              hx-target="#dialog">
                              Annuler candidature 
                            </a>
                        {% endif %}

                      </div>
                    </div>
                  </td>
                </tr>
                {% endfor %}
            </tbody>
          </table>
          {% include 'components/pagination.html' with include_items="[name=per_page], [name=search], [name=locations_select], [name=schools_select]" %}

        </div>
      </div>
    </div>
  </div>
</div>

<script>
$('img.lazy').lazyload({
  load: function() { $(this).removeClass("lazyload"); },
});

  if (typeof(feather) !== 'undefined') {
    feather.replace();
  }

</script>
