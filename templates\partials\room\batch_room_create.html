{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">AJOUT MULTIPLE DE SALLES D'EXAMEN {{ exam|upper }}</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold">Création de plusieurs salles</div>
        </div>
        
        <div class="form-row">
            {% if user.role == ROLE_COMMISSION_NATIONALE %}
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.exam.id_for_label }}">Examen *</label>
                 {% render_field form.exam class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.location.id_for_label }}">Localité *</label>
                 {% render_field form.location class='form-control' hx-get='centres_commission/' hx-target='#location_centers' hx-include='[name=exam]' %}
            </div>
            {% endif %}
            <div class="form-group mb-2 col-md-12" id="location_centers">
                <label for="{{ form.center.id_for_label }}">Centre *</label>
                 {% render_field form.center class='form-control' %}
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group mb-2 col-md-4">
                <label for="{{ form.num_rooms.id_for_label }}">Nombre de salles à créer *</label>
                {% render_field form.num_rooms class='form-control' min="1" %}
                <small class="form-text text-muted">Nombre total de salles à créer</small>
            </div>
            <div class="form-group mb-2 col-md-4">
                <label for="{{ form.students_per_room.id_for_label }}">Capacité par salle *</label>
                {% render_field form.students_per_room class='form-control' min="1" %}
                <small class="form-text text-muted">Nombre d'élèves par salle</small>
            </div>
            <div class="form-group mb-2 col-md-4">
                <label for="{{ form.starting_number.id_for_label }}">Numéro de départ *</label>
                {% render_field form.starting_number class='form-control' min="1" %}
                <small class="form-text text-muted">Premier numéro de salle</small>
            </div>
        </div>
        
        <div class="alert alert-info mt-3">
            <span data-feather="info" class="mr-2"></span>
            <strong>Aperçu:</strong> 
            <span id="preview-text">
                Création de <span id="num-rooms">{{ form.num_rooms.value|default:"0" }}</span> salles 
                (numéros <span id="start-num">{{ form.starting_number.value|default:"1" }}</span> 
                à <span id="end-num">{{ form.starting_number.value|default:"1" }}</span>) 
                avec une capacité de <span id="capacity">{{ form.students_per_room.value|default:"0" }}</span> élèves par salle.
            </span>
        </div>
    </div>
    
    <div class="modal-footer">
        <button type="submit" class="btn btn-success">Créer les salles</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>
</form>

<script>
    // Update the preview text when form values change
    function updatePreview() {
        const numRooms = parseInt(document.getElementById('{{ form.num_rooms.id_for_label }}').value) || 0;
        const startNum = parseInt(document.getElementById('{{ form.starting_number.id_for_label }}').value) || 1;
        const capacity = parseInt(document.getElementById('{{ form.students_per_room.id_for_label }}').value) || 0;
        
        document.getElementById('num-rooms').textContent = numRooms;
        document.getElementById('start-num').textContent = startNum;
        document.getElementById('end-num').textContent = startNum + numRooms - 1;
        document.getElementById('capacity').textContent = capacity;
    }
    
    // Add event listeners to form fields
    document.getElementById('{{ form.num_rooms.id_for_label }}').addEventListener('input', updatePreview);
    document.getElementById('{{ form.starting_number.id_for_label }}').addEventListener('input', updatePreview);
    document.getElementById('{{ form.students_per_room.id_for_label }}').addEventListener('input', updatePreview);
    
    // Initialize preview
    updatePreview();
</script>
