{% load humanize %}
{% load custom_filters %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}"
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Centres</li>
      <li class="breadcrumb-item active" aria-current="page">Statistiques par localité</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">STATISTIQUES D'AFFECTATION DES ÉLÈVES PAR LOCALITÉ</h5>
  </div>

  <!-- Summary Statistics Card -->
  <div class="card mb-4 shadow-sm">
    <div class="card-header bg-light">
      <h6 class="mb-0 font-weight-bold">Résumé des affectations - Examen {{ selected_exam|upper }}</h6>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-8">
          <div class="row">
            <div class="col-md-4">
              <div class="card border-0 shadow-sm h-75 bg-light">
                <div class="card-body text-center">
                  <h6 class="mb-2 text-primary">Élèves inscrits</h6>
                  <h2 class="font-weight-bold text-primary">{{ stats.total_students|intcomma }}</h2>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card border-0 shadow-sm h-75 bg-light">
                <div class="card-body text-center">
                  <h6 class="text-success mb-2">Élèves avec centre</h6>
                  <h2 class="font-weight-bold text-success">{{ stats.students_with_center|intcomma }}</h2>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card border-0 shadow-sm h-75 bg-light">
                <div class="card-body text-center">
                  <h6 class="text-danger mb-2">Élèves sans centre</h6>
                  <h2 class="font-weight-bold text-danger">{{ stats.students_without_center|intcomma }}</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-0 shadow-sm h-75">
            <div class="card-body text-center">
              <h6 class="mb-2">Taux d'affectation</h6>
              <div class="progress mb-3" style="height: 25px;">
                <div class="progress-bar bg-success" role="progressbar"
                     style="width: {{ stats.completion_percentage }}%;"
                     aria-valuenow="{{ stats.completion_percentage }}"
                     aria-valuemin="0" aria-valuemax="100">
                  {{ stats.completion_percentage|floatformat:1 }}%
                </div>
              </div>
              <small class="text-muted">
                {{ stats.students_with_center|intcomma }} élèves affectés sur {{ stats.total_students|intcomma }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Locations Table -->
  <div class="row" id="table-wrapper">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">LOCALITÉ</th>
                <th scope="col" class="text-white">ÉLÈVES INSCRITS</th>
                <th scope="col" class="text-white">ÉLÈVES AVEC CENTRE</th>
                <th scope="col" class="text-white">ÉLÈVES SANS CENTRE</th>
                <th scope="col" class="text-white">TAUX D'AFFECTATION</th>
                <th scope="col" class="text-white">STATUT</th>
              </tr>
            </thead>
            <tbody>
              {% for location in locations %}
              <tr>
                <td>{{ location }}</td>
                <td>{{ location.total_students }}</td>
                <td>{{ location.students_with_center }}</td>
                <td>{{ location.students_without_center }}</td>
                <td>
                  {% if location.total_students > 0 %}
                    {{ location.students_with_center|floatformat:0|intcomma }} / {{ location.total_students|floatformat:0|intcomma }}
                    {% with percentage=location.students_with_center|floatformat:0 %}
                      {% widthratio location.students_with_center location.total_students 100 as percentage %}
                      ({{ percentage|floatformat:1 }}%)
                    {% endwith %}
                  {% else %}
                    0%
                  {% endif %}
                </td>
                <td>
                  {% if location.status == 'Complet' %}
                    <span class="badge badge-success">Complet</span>
                  {% else %}
                    <span class="badge badge-warning">Incomplet</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#datatable')) {
      $('#datatable').DataTable().destroy();
    }


    if (typeof(feather) !== 'undefined') {
      feather.replace();
    }
  });
</script>
