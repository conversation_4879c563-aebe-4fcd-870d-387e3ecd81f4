{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Cartes Scolaires</li>
    </ol>
  </nav>
  <div class="pb-3 mt-3">
    <h5 class="font-weight-bold">
      {% if request.GET.filtre == 'DEMANDES' %}
      CARTES SCOLAIRE EN ATTENTE 
      {% elif request.GET.filtre == 'PRODUITES' %}
      CARTES SCOLAIRES PRODUITES
      {% elif request.GET.filtre == 'LIVREES' %}
      CARTES SCOLAIRES LIVREES
      {% else %}
      GESTION DES CARTES SCOLAIRES
      {% endif %}
    
      > {% if level == 'AUTRES' %} CLASSES INTERMEDIAIRES {% else %} {{ level }} {% endif %}
    </h5>
  </div>
  <div class="container-fluid">
    <div class="alert alert-success border">
      <span data-feather="info" class="align-middle feather-16 feather-success"></span>
      Les demandes de cartes scolaires pour les classes d'examens seront générées automatiquement. 
    </div>
  </div>

  <div class="container d-flex mb-3 justify-content-between">
    {% if not request.GET.filtre or request.GET.filtre == 'DEMANDES' %} <span class="badge badge-warning p-2">DEMANDES: {{ stats.pending|default:'0' }} </span>{% endif %}
    {% if not request.GET.filtre or request.GET.filtre == 'PRODUITES' %}<span class="badge badge-info p-2">PRODUITES: {{ stats.manufactured|default:'0' }}</span>{% endif %}
    {% if not request.GET.filtre or request.GET.filtre == 'LIVREES' %}<span class="badge badge-success p-2">LIVREES: {{ stats.shipped|default:'0' }}</span>{% endif %}
    <span class="badge badge-secondary p-2"><span data-feather="dollar-sign" class="feather-16"></span> PAYEES: {{ stats.paid|default:'0' }}</span>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
      {% if perms.exams.add_studentcard %}
      <div>
        <a class="btn btn-success text-white"
           hx-get="{% url 'student_card_create' %}"
           hx-target="#dialog">
           + Nouvelle demande</a>
      </div>
      {% endif %}
  </div>

  {% include 'components/tabs.html' with nav_items=nav_items vals_items=True %}
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>