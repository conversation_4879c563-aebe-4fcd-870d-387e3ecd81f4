{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">DRENA</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">LISTE DES DELEGUES</h5>
  </div>

  {% if perms.users.add_customuser %}
    <div class="container-fluid p-0 mb-3">
      <a class="btn btn-success text-white"
         hx-get="{% url 'user_add' %}"
         hx-target="#dialog"> 
         <span data-feather="plus" class="feather-16"></span> Ajouter </a>
    </div>
  {% endif %}

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-actions table-striped table-hover mb-0" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">NOM</th>
                <th scope="col" class="text-white">PRENOMS</th>
                <th scope="col" class="text-white">SEXE</th>
                <th scope="col" class="text-white">DRENA EN CHARGE</th>
                <th scope="col" class="text-white">NOM D'UTILISATEUR</th>
                <th scope="col" class="text-white">MOT DE PASSE</th>
                <th scope="col" class="text-white">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for user in users %}
              <tr>
                <td>{{ user.last_name }}</td>
                <td>{{ user.first_name }}</td>
                <td>{{ user.gender }}</td>
                <td>{{ user.get_locations }}</td>
                <td>{{ user.username }}</td>
                <td>{{ user.initial_password }}</td>
                  <td>
                    <div class="dropdown">
                      <a class="btn btn-sm border" href="#"
                      hx-get="{% url 'user_edit' user.pk %}"
                      hx-target="#dialog"> <span data-feather='edit'></span> </a>
                    </div>
                  </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready( function() {
    feather.replace();
    $('#datatable').DataTable({});
});
</script>
