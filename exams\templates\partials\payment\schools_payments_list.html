{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}" 
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Versements</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      <strong class="text-muted">
      LISTE DES VERSEMENTS {{ year }}
      {% if user.role == ROLE_COMMISSION_LOCALE %} DES ECOLES DE {{ user.localcommission }}
      {% endif %}
      </strong>
    </h5>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
  {% if perms.exams.add_schoolpayment %}
      <div>
        <a class="btn btn-success text-white"
           hx-get="{% url 'payment_add' %}"
           hx-target="#dialog">
           + Ajouter un versement</a>
      </div>
  {% endif %}
  <div>
    <div class="dropdown">
      <button class="btn btn-warning dropdown" type="button" 
              data-toggle="dropdown" aria-expanded="false">
        <i data-feather="file-text"></i> Imprimer
      </button>
      <div class="dropdown-menu">
          <a class="dropdown-item" 
            href="{% url 'payments_pdf' %}">Resumé des versements</a>
        </div>
    </div>
  </div>
</div>

  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                <th scope="col" class="text-white">DATE</th>
                {% if user.role == ROLE_COMMISSION_NATIONALE %}
                <th scope="col" class="text-white">LOCALITE</th>
                {% endif %}

                <th scope="col" class="text-white">ECOLE</th>
                <th scope="col" class="text-white">VERSEMENT</th>

                {% if perms.exams.change_schoolpayment %}
                <th scope="col" class="text-white">MOD.</th>
                {% endif %}
              </tr>
            </thead>
            <tbody>
              {% for payment in object_list %}
              <tr>
                <td>{{ payment.date|date:'d/m/Y' }}</td>
                {% if user.role == ROLE_COMMISSION_NATIONALE %}
                <td>{{ payment.school.local_commission }}</td>
                {% endif %}
                <td style="min-width: 115px">{{ payment.school.name }}</td>
                <td class="text-right"><strong>{{ payment.amount|intcomma }} F</strong></td>

                {% if perms.exams.change_schoolpayment %}
                <td>
                  <a class="btn btn-sm border" 
                     href=""
                     hx-get="{% url 'payment_edit' payment.id %}"
                     hx-target="#dialog">
                    <span data-feather="edit"></span>
                  </a>
                </td>
                {% endif %}
                
              </tr>
              {% endfor %}
            </tbody>
            <tfoot class="bg-warning">
              <tr>
                <td><strong>TOTAL DES VERSEMENTS</strong></td>
                <td></td>
                {% if user.role == ROLE_COMMISSION_NATIONALE %}
                <td></td>
                {% endif %}
                <td class="text-right"><strong>{{ payments_total|intcomma }} F</strong></td>
                {% if user.role == ROLE_COMMISSION_NATIONALE %}
                <td></td>
                {% endif %}
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace(); 
  }
</script>