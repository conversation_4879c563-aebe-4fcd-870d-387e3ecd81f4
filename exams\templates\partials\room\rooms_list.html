{% load static %}
{% load humanize %}

<div class="container-fluid mt-2">
  <!-- BreadCrumb -->
  <nav aria-label="breadcrumb" role="navigation">
    <ol class="breadcrumb adminx-page-breadcrumb">
      <li class="breadcrumb-item">
        <a href="" hx-get="{% url 'home' %}"
           hx-target="#main-content" hx-push-url="{% url 'home' %}">Accueil</a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">Salles</li>
    </ol>
  </nav>

  <div class="pb-3 mt-3">
    <h5 class="fw-bold">
      <strong class="text-muted">
      LISTE DES SALLES DANS LES CENTRES CHERIFLA
      {% if user.role == ROLE_COMMISSION_LOCALE %}
        DE {{ user.localcommission }}
      {% endif %}
      </strong>
    </h5>
  </div>
  <div class="container-fluid p-0 mb-3 d-flex flex-row justify-content-between">
  {% if perms.exams.add_room %}
      <div>
        <a class="btn btn-success text-white mr-2"
           hx-get="{% url 'room_add' %}?exam={{exam}}"
           hx-target="#dialog">
           <i data-feather="plus" class="feather-16 mr-1"></i> Ajouter une salle</a>
        <a class="btn btn-primary text-white"
           hx-get="{% url 'batch_room_add' %}?exam={{exam}}"
           hx-target="#dialog">
           <i data-feather="layers" class="feather-16 mr-1"></i> Ajouter plusieurs salles</a>
      </div>
  {% endif %}
</div>
  <div class="row">
    <div class="col">
      <div class="card mb-grid">
        <div class="table-responsive-md">
          <table class="table table-sm table-actions table-striped table-hover mb-0 datatable" data-table id="datatable">
            <thead class="bg-secondary">
              <tr>
                  <th scope="col" class="text-white" style="min-width: 100px">CENTRE</th>
                <th scope="col" class="text-white">SALLE N°</th>
                <th scope="col" class="text-white">CAPACITE</th>
                <th scope="col" class="text-white">CANDIDATS RECUS</th>
                <th scope="col" class="text-white align-left">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {% for room in rooms %}
              <tr>
                <td>{{ room.center }}</td>
                <td>{{ room.number }}</td>
                <td style="min-width: 115px">{{ room.capacity }}</td>
                <td><strong>{{ room.candidates_count }}</strong></td>

                <td class="text-center">
                  <div class="dropdown">
                    <button class="btn btn-primary btn-sm dropdown" type="button" data-toggle="dropdown" aria-expanded="false">
                    <i data-feather="more-horizontal"></i>
                    </button>
                    <div class="dropdown-menu">
                      {% if not room.center.complete and perms.exams.change_center %}
                      <a class="dropdown-item" href="#"
                      hx-get="{% url 'room_edit' room.id %}?exam={{exam}}"
                      hx-target="#dialog">Modifier</a>
                      {% endif %}
                      {% if not room.center.complete and perms.exams.delete_center %}
                      <a class="dropdown-item" href="#"
                      hx-get="{% url 'room_delete' room.id %}"
                      hx-target="#dialog"
                      hx-include="[name=exam]">Supprimer</a>
                      <div class="dropdown-divider"></div>
                      {% endif %}
                      <a class="dropdown-item" href="">
                          Listes candidats
                        </a>
                      <a class="dropdown-item"
                           href="">
                          Fiches de table
                        </a>
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== 'undefined') {
    feather.replace();
  }
</script>