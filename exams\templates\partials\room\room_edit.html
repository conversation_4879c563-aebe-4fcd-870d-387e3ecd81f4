{% load widget_tweaks %}

<form action="{{ request.path }}" method="post" class="modal-content" id="modal-content">
    {% csrf_token %}

    <div class="modal-header text-success bg-light-green">
        <SPAN class="modal-title fw-bold">SALLE D'EXAMEN {{ exam|upper }}</SPAN>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-row mb-3 container--fluid">
            <div class="lead text-muted fw-bold"> Infos Salle</div>
        </div>
        <div class="form-row">
            {% if user.role == ROLE_COMMISSION_NATIONALE %}
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.exam.id_for_label }}">Examen *</label>
                 {% render_field form.exam class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-6">
                <label for="{{ form.location.id_for_label }}">Localité *</label>
                 {% render_field form.location class='form-control' hx-get='centres_commission/' hx-target='#location_centers' hx-include='[name=exam]' %}
            </div>
            {% endif %}
            <div class="form-group mb-2 col-md-6" id="location_centers">
                <label for="{{ form.center.id_for_label }}">Centre *</label>
                 {% render_field form.center class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-3">
                <label for="{{ form.number.id_for_label }}">Numéro de la salle</label>
                {% render_field form.number class='form-control' %}
            </div>
            <div class="form-group mb-2 col-md-3">
                <label for="{{ form.capacity.id_for_label }}">Capacité de la salle *</label>
                {% render_field form.capacity class='form-control' %}
            </div>
        </div>

    </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-success"> Enregistrer</button>
        <button class="btn btn-danger" data-dismiss="modal">Fermer</button>
    </div>

</form>